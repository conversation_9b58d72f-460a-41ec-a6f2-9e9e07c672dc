# ========================================
# PROTECCIÓN AVANZADA CONTRA VULNERABILIDADES
# ========================================

# Deshabilitar listado de directorios
Options -Indexes

# Proteger archivos sensibles
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf|config)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acceso a archivos de configuración
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# ========================================
# PROTECCIÓN EXTREMA CONTRA BOTS Y SCRAPERS
# ========================================

RewriteEngine On

# Bloquear User Agents maliciosos y herramientas de hacking
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (Scrapy|scrapy|bot|Bot|BOT|spider|Spider|SPIDER) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (sqlmap|nmap|masscan|zmap|nuclei|gobuster|dirb|dirbuster) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (burp|owasp|w3af|skipfish|wpscan|joomscan|droopescan) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (acunetix|nessus|openvas|qualys|rapid7|metasploit) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (havij|pangolin|bbqsql|bsqlbf|marathon|sqlninja) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (hydra|medusa|brutus|ncrack|patator|crowbar) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (beef|xsser|commix|sqliv|fimap|uniscan) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (whatweb|wapiti|arachni|vega|grendel|paros) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (appscan|webinspect|hailstorm|webscarab|proxystrike) [NC]
RewriteRule .* - [F,L]

# Bloquear herramientas de línea de comandos y lenguajes de programación
RewriteCond %{HTTP_USER_AGENT} (perl|php|ruby|go-http|node|axios|requests|urllib) [NC]
RewriteRule .* - [F,L]

# Bloquear navegadores automatizados
RewriteCond %{HTTP_USER_AGENT} (selenium|phantomjs|headless|chrome-headless|puppeteer) [NC]
RewriteRule .* - [F,L]

# Bloquear IPs sospechosas (añade las IPs que detectes como maliciosas)
# RewriteCond %{REMOTE_ADDR} ^123\.456\.789\.000$
# RewriteRule .* - [F,L]

# ========================================
# PROTECCIÓN EXTREMA CONTRA ATAQUES COMUNES
# ========================================

# Protección avanzada contra inyección SQL
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} concat.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*select.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*all.*select.* [NC,OR]
RewriteCond %{QUERY_STRING} \-[sdcr].*(allow_url_include|allow_url_fopen|safe_mode|disable_functions|auto_prepend_file) [NC,OR]
RewriteCond %{QUERY_STRING} (select.*from|insert.*into|update.*set|delete.*from|drop.*table|create.*table) [NC,OR]
RewriteCond %{QUERY_STRING} (exec|system|shell_exec|passthru|eval|base64_decode|gzinflate|str_rot13) [NC,OR]
RewriteCond %{QUERY_STRING} (benchmark|sleep|waitfor|delay|pg_sleep|dbms_pipe) [NC,OR]
RewriteCond %{QUERY_STRING} (information_schema|mysql\.user|pg_user|sys\.objects) [NC,OR]
RewriteCond %{QUERY_STRING} (load_file|into.*outfile|into.*dumpfile) [NC,OR]
RewriteCond %{QUERY_STRING} (0x[0-9a-f]+|char\(|ascii\(|ord\(|hex\() [NC,OR]
RewriteCond %{QUERY_STRING} (waitfor.*delay|benchmark.*\(|sleep.*\() [NC]
RewriteRule .* - [F,L]

# Protección extrema contra XSS
RewriteCond %{QUERY_STRING} (\<|%3C).*(script|iframe|object|embed|applet|meta|link|style).*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (javascript:|vbscript:|data:|about:|chrome:|resource:) [NC,OR]
RewriteCond %{QUERY_STRING} (onload|onerror|onclick|onmouseover|onfocus|onblur).*= [NC,OR]
RewriteCond %{QUERY_STRING} (alert\(|confirm\(|prompt\(|document\.|window\.|eval\() [NC]
RewriteRule .* - [F,L]

# Protección contra Local File Inclusion (LFI)
RewriteCond %{QUERY_STRING} (\.\./|\.\.\%2f|\.\.\%5c|%2e%2e%2f|%2e%2e%5c) [NC,OR]
RewriteCond %{QUERY_STRING} (etc/passwd|proc/self/environ|/var/log/) [NC,OR]
RewriteCond %{QUERY_STRING} (boot\.ini|win\.ini|system\.ini) [NC]
RewriteRule .* - [F,L]

# Protección contra Remote File Inclusion (RFI)
RewriteCond %{QUERY_STRING} (http://|https://|ftp://|php://|expect://|data:) [NC]
RewriteRule .* - [F,L]

# Protección contra Command Injection
RewriteCond %{QUERY_STRING} (\||;|&|`|\$\(|\${|<\(|>\() [NC,OR]
RewriteCond %{QUERY_STRING} (nc |netcat|telnet|wget |curl |lynx) [NC,OR]
RewriteCond %{QUERY_STRING} (/bin/|/usr/bin/|/sbin/|cmd\.exe|powershell) [NC]
RewriteRule .* - [F,L]

# ========================================
# LIMITACIÓN DE VELOCIDAD Y ACCESOS
# ========================================

# Limitar tamaño de archivos subidos
LimitRequestBody 10485760

# Timeout de conexión
Timeout 300

# ========================================
# HEADERS DE SEGURIDAD
# ========================================

<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always append X-Frame-Options SAMEORIGIN
    
    # Protección XSS
    Header set X-XSS-Protection "1; mode=block"
    
    # Prevenir MIME sniffing
    Header set X-Content-Type-Options nosniff
    
    # Política de referrer
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy básica
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
    
    # Ocultar información del servidor
    Header unset Server
    Header unset X-Powered-By
</IfModule>

# ========================================
# PROTECCIÓN DE DIRECTORIOS ESPECÍFICOS
# ========================================

# Bloquear acceso a directorios admin, config, etc.
RewriteRule ^(admin|config|includes|logs|backup|private)/ - [F,L]

# Redireccionar intentos de acceso a archivos PHP directamente
RewriteCond %{THE_REQUEST} /([^/]+)\.php[\s?] [NC]
RewriteRule ^([^/]+)\.php$ /$1 [R=301,L]

# ========================================
# LOGGING DE SEGURIDAD
# ========================================

# Log de intentos de acceso a archivos prohibidos
RewriteCond %{REQUEST_URI} \.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf|config)$ [NC]
RewriteRule .* - [E=SECURITY_ALERT:prohibited_file_access,L]

# Log de intentos de inyección SQL
RewriteCond %{QUERY_STRING} (union.*select|concat.*\(|script.*\>|<.*script) [NC]
RewriteRule .* - [E=SECURITY_ALERT:sql_injection_attempt,L]

# ========================================
# BLOQUEO DE PAÍSES ESPECÍFICOS (OPCIONAL)
# ========================================

# Descomenta y ajusta según necesites
# RewriteCond %{ENV:GEOIP_COUNTRY_CODE} ^(CN|RU|KP)$
# RewriteRule .* - [F,L]

# ========================================
# PROTECCIÓN ADICIONAL CONTRA EXPLOITS
# ========================================

# Bloquear intentos de directory traversal
RewriteCond %{QUERY_STRING} \.\./\.\./\.\./
RewriteRule .* - [F,L]

# Bloquear intentos de acceso a archivos de sistema
RewriteCond %{REQUEST_URI} (etc/passwd|proc/|dev/|sys/) [NC]
RewriteRule .* - [F,L]

# Bloquear métodos HTTP peligrosos
RewriteCond %{REQUEST_METHOD} ^(TRACE|DELETE|TRACK|PUT|PATCH|OPTIONS) [NC]
RewriteRule .* - [F,L]

# ========================================
# BLOQUEO DE EMERGENCIA
# ========================================

# Si existe el archivo de bloqueo de emergencia, denegar todo acceso excepto al admin
RewriteCond %{REQUEST_FILENAME} !emergency_lockdown.flag
RewriteCond %{DOCUMENT_ROOT}/emergency_lockdown.flag -f
RewriteCond %{REQUEST_URI} !^/(index_extreme\.php|dashboard_extreme\.php)$
RewriteRule .* - [F,L]

# ========================================
# PROTECCIÓN CONTRA ENUMERACIÓN
# ========================================

# Bloquear intentos de enumeración de archivos comunes
RewriteCond %{REQUEST_URI} (wp-admin|wp-login|wp-content|wp-includes|admin|administrator|login|phpmyadmin|cpanel|webmail) [NC]
RewriteRule .* - [F,L]

# Bloquear extensiones peligrosas
RewriteCond %{REQUEST_URI} \.(asp|aspx|jsp|cgi|pl|py|rb|sh|exe|dll|bat|cmd)$ [NC]
RewriteRule .* - [F,L]

# ========================================
# PROTECCIÓN CONTRA FINGERPRINTING
# ========================================

# Ocultar información del servidor
ServerTokens Prod
Header unset Server
Header unset X-Powered-By
Header unset X-AspNet-Version
Header unset X-AspNetMvc-Version

# Respuestas falsas para confundir scanners
RewriteCond %{REQUEST_URI} (robots\.txt|sitemap\.xml|favicon\.ico)$ [NC]
RewriteRule .* /fake_response.php [L]
