# ========================================
# SISTEMA DE CONTROL DE ACCESO GEOGRÁFICO
# ========================================

# Deshabilitar listado de directorios
Options -Indexes

# Proteger archivos sensibles
<FilesMatch "\.(json|log|txt)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acceso a archivos de configuración
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Redirigir todo al index.php para control centralizado
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]


