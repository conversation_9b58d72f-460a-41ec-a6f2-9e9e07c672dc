# ========================================
# SISTEMA DE CONTROL DE ACCESO GEOGRÁFICO
# ========================================

# Deshabilitar listado de directorios
Options -Indexes

# Proteger archivos sensibles
<FilesMatch "\.(json|log|txt)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acceso a archivos de configuración
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Bloquear herramientas de scraping y bots
RewriteEngine On

# Bloquear User Agents de herramientas automatizadas
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} (wget|curl|python|java|libwww|httpclient|scanner|bot|spider|crawler|scraper) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (postman|insomnia|httpie|requests|urllib|mechanize|scrapy) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (nikto|sqlmap|nmap|masscan|nuclei|gobuster|dirb|burp) [NC]
RewriteRule .* - [F,L]

# Bloquear acceso directo a archivos PHP (excepto index.php)
RewriteCond %{REQUEST_URI} \.php$ [NC]
RewriteCond %{REQUEST_URI} !^/index\.php$ [NC]
RewriteRule .* - [F,L]

# Redirigir todo al index.php para control centralizado
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]


