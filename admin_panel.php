<?php
// Panel de administración - Solo accesible desde Panamá

if (!($_SESSION['admin'] ?? false)) {
    die('Acceso denegado');
}

// Procesar acciones del admin
if ($_POST['accion'] ?? false) {
    $accion = $_POST['accion'];
    $ip = $_POST['ip'] ?? '';
    
    switch ($accion) {
        case 'aprobar_solicitud':
            $solicitudId = $_POST['solicitud_id'];
            $tipoAcceso = $_POST['tipo_acceso'];
            $rutasPermitidas = $_POST['rutas_permitidas'] ?? [];
            
            // Cargar solicitudes
            $solicitudes = json_decode(file_get_contents('solicitudes_acceso.json'), true);
            
            // Encontrar y aprobar la solicitud
            foreach ($solicitudes as &$solicitud) {
                if ($solicitud['id'] === $solicitudId) {
                    $solicitud['estado'] = 'aprobada';
                    $solicitud['aprobada_por'] = 'admin';
                    $solicitud['fecha_aprobacion'] = date('Y-m-d H:i:s');
                    
                    // Añadir a accesos aprobados
                    $aprobados = [];
                    if (file_exists('accesos_aprobados.json')) {
                        $aprobados = json_decode(file_get_contents('accesos_aprobados.json'), true);
                    }
                    
                    $aprobados[$solicitud['ip']] = [
                        'estado' => 'activo',
                        'tipo_acceso' => $tipoAcceso,
                        'rutas_permitidas' => $rutasPermitidas,
                        'fecha_aprobacion' => date('Y-m-d H:i:s'),
                        'pais' => $solicitud['pais'],
                        'isp' => $solicitud['isp']
                    ];
                    
                    file_put_contents('accesos_aprobados.json', json_encode($aprobados, JSON_PRETTY_PRINT));
                    break;
                }
            }
            
            file_put_contents('solicitudes_acceso.json', json_encode($solicitudes, JSON_PRETTY_PRINT));
            $mensaje = "Solicitud aprobada exitosamente";
            break;
            
        case 'denegar_solicitud':
            $solicitudId = $_POST['solicitud_id'];
            $solicitudes = json_decode(file_get_contents('solicitudes_acceso.json'), true);
            
            foreach ($solicitudes as &$solicitud) {
                if ($solicitud['id'] === $solicitudId) {
                    $solicitud['estado'] = 'denegada';
                    $solicitud['denegada_por'] = 'admin';
                    $solicitud['fecha_denegacion'] = date('Y-m-d H:i:s');
                    break;
                }
            }
            
            file_put_contents('solicitudes_acceso.json', json_encode($solicitudes, JSON_PRETTY_PRINT));
            $mensaje = "Solicitud denegada";
            break;
            
        case 'banear_ip':
            $motivo = $_POST['motivo'] ?? 'Sin motivo especificado';
            $listaNegra = [];
            if (file_exists('lista_negra.json')) {
                $listaNegra = json_decode(file_get_contents('lista_negra.json'), true);
            }
            
            $listaNegra[$ip] = [
                'fecha_ban' => date('Y-m-d H:i:s'),
                'motivo' => $motivo,
                'baneado_por' => 'admin'
            ];
            
            file_put_contents('lista_negra.json', json_encode($listaNegra, JSON_PRETTY_PRINT));
            $mensaje = "IP $ip baneada exitosamente";
            break;
            
        case 'desbanear_ip':
            $listaNegra = [];
            if (file_exists('lista_negra.json')) {
                $listaNegra = json_decode(file_get_contents('lista_negra.json'), true);
            }
            
            if (isset($listaNegra[$ip])) {
                unset($listaNegra[$ip]);
                file_put_contents('lista_negra.json', json_encode($listaNegra, JSON_PRETTY_PRINT));
                $mensaje = "IP $ip desbaneada exitosamente";
            }
            break;
            
        case 'revocar_acceso':
            $aprobados = [];
            if (file_exists('accesos_aprobados.json')) {
                $aprobados = json_decode(file_get_contents('accesos_aprobados.json'), true);
            }
            
            if (isset($aprobados[$ip])) {
                $aprobados[$ip]['estado'] = 'revocado';
                file_put_contents('accesos_aprobados.json', json_encode($aprobados, JSON_PRETTY_PRINT));
                $mensaje = "Acceso revocado para IP $ip";
            }
            break;
    }
}

// Cargar datos para mostrar
$solicitudes = [];
if (file_exists('solicitudes_acceso.json')) {
    $solicitudes = json_decode(file_get_contents('solicitudes_acceso.json'), true);
    $solicitudes = array_reverse($solicitudes); // Más recientes primero
}

$aprobados = [];
if (file_exists('accesos_aprobados.json')) {
    $aprobados = json_decode(file_get_contents('accesos_aprobados.json'), true);
}

$listaNegra = [];
if (file_exists('lista_negra.json')) {
    $listaNegra = json_decode(file_get_contents('lista_negra.json'), true);
}

$accesos = [];
if (file_exists('accesos.log')) {
    $lineas = file('accesos.log', FILE_IGNORE_NEW_LINES);
    $accesos = array_map('json_decode', array_slice($lineas, -50)); // Últimos 50
    $accesos = array_reverse($accesos);
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Panel de Control - RogsMediaTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #2c3e50; color: white; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { background: #34495e; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }
        .section { background: #34495e; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #555; }
        th { background: #2c3e50; }
        .btn { padding: 8px 15px; margin: 2px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-info { background: #3498db; color: white; }
        .status-pendiente { color: #f39c12; }
        .status-aprobada { color: #27ae60; }
        .status-denegada { color: #e74c3c; }
        .status-activo { color: #27ae60; }
        .status-revocado { color: #e74c3c; }
        .form-inline { display: flex; gap: 10px; align-items: center; margin-bottom: 15px; }
        .form-inline input, .form-inline select { padding: 8px; border-radius: 5px; border: 1px solid #555; background: #2c3e50; color: white; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; background: #27ae60; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎛️ Panel de Control - RogsMediaTV</h1>
            <p>Gestión de Accesos y Solicitudes</p>
            <a href="?admin=logout" class="btn btn-danger">Cerrar Sesión</a>
        </div>

        <?php if (isset($mensaje)): ?>
            <div class="alert"><?php echo $mensaje; ?></div>
        <?php endif; ?>

        <div class="grid">
            <!-- Solicitudes Pendientes -->
            <div class="section">
                <h2>📋 Solicitudes de Acceso Pendientes</h2>
                <?php 
                $pendientes = array_filter($solicitudes, function($s) { return $s->estado === 'pendiente'; });
                if (empty($pendientes)): 
                ?>
                    <p>No hay solicitudes pendientes</p>
                <?php else: ?>
                    <table>
                        <tr>
                            <th>Fecha</th>
                            <th>IP</th>
                            <th>País</th>
                            <th>ISP</th>
                            <th>Ruta</th>
                            <th>Acciones</th>
                        </tr>
                        <?php foreach ($pendientes as $solicitud): ?>
                            <tr>
                                <td><?php echo $solicitud->timestamp; ?></td>
                                <td><?php echo $solicitud->ip; ?></td>
                                <td><?php echo $solicitud->pais; ?></td>
                                <td><?php echo $solicitud->isp; ?></td>
                                <td><?php echo $solicitud->ruta_solicitada; ?></td>
                                <td>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="accion" value="aprobar_solicitud">
                                        <input type="hidden" name="solicitud_id" value="<?php echo $solicitud->id; ?>">
                                        <select name="tipo_acceso" required>
                                            <option value="total">Acceso Total</option>
                                            <option value="limitado">Acceso Limitado</option>
                                        </select>
                                        <button type="submit" class="btn btn-success">Aprobar</button>
                                    </form>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="accion" value="denegar_solicitud">
                                        <input type="hidden" name="solicitud_id" value="<?php echo $solicitud->id; ?>">
                                        <button type="submit" class="btn btn-danger">Denegar</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
            </div>

            <!-- Accesos Aprobados -->
            <div class="section">
                <h2>✅ Accesos Aprobados</h2>
                <?php if (empty($aprobados)): ?>
                    <p>No hay accesos aprobados</p>
                <?php else: ?>
                    <table>
                        <tr>
                            <th>IP</th>
                            <th>País</th>
                            <th>Tipo</th>
                            <th>Estado</th>
                            <th>Acciones</th>
                        </tr>
                        <?php foreach ($aprobados as $ip => $acceso): ?>
                            <tr>
                                <td><?php echo $ip; ?></td>
                                <td><?php echo $acceso['pais']; ?></td>
                                <td><?php echo $acceso['tipo_acceso']; ?></td>
                                <td class="status-<?php echo $acceso['estado']; ?>"><?php echo strtoupper($acceso['estado']); ?></td>
                                <td>
                                    <?php if ($acceso['estado'] === 'activo'): ?>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="accion" value="revocar_acceso">
                                            <input type="hidden" name="ip" value="<?php echo $ip; ?>">
                                            <button type="submit" class="btn btn-warning">Revocar</button>
                                        </form>
                                    <?php endif; ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="accion" value="banear_ip">
                                        <input type="hidden" name="ip" value="<?php echo $ip; ?>">
                                        <input type="text" name="motivo" placeholder="Motivo del ban" required>
                                        <button type="submit" class="btn btn-danger">Banear</button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </table>
                <?php endif; ?>
            </div>
        </div>

        <!-- IPs Baneadas -->
        <div class="section">
            <h2>🚫 IPs Baneadas</h2>
            <?php if (empty($listaNegra)): ?>
                <p>No hay IPs baneadas</p>
            <?php else: ?>
                <table>
                    <tr>
                        <th>IP</th>
                        <th>Fecha Ban</th>
                        <th>Motivo</th>
                        <th>Acciones</th>
                    </tr>
                    <?php foreach ($listaNegra as $ip => $ban): ?>
                        <tr>
                            <td><?php echo $ip; ?></td>
                            <td><?php echo $ban['fecha_ban']; ?></td>
                            <td><?php echo $ban['motivo']; ?></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="accion" value="desbanear_ip">
                                    <input type="hidden" name="ip" value="<?php echo $ip; ?>">
                                    <button type="submit" class="btn btn-success">Desbanear</button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            <?php endif; ?>
        </div>

        <!-- Log de Accesos Recientes -->
        <div class="section">
            <h2>📊 Accesos Recientes</h2>
            <table>
                <tr>
                    <th>Fecha</th>
                    <th>IP</th>
                    <th>País</th>
                    <th>ISP</th>
                    <th>Ruta</th>
                    <th>Estado</th>
                </tr>
                <?php foreach (array_slice($accesos, 0, 20) as $acceso): ?>
                    <tr>
                        <td><?php echo $acceso->timestamp; ?></td>
                        <td><?php echo $acceso->ip; ?></td>
                        <td><?php echo $acceso->pais; ?></td>
                        <td><?php echo $acceso->isp; ?></td>
                        <td><?php echo $acceso->ruta; ?></td>
                        <td><?php echo $acceso->acceso; ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
    </div>

    <script>
        // Auto-refresh cada 30 segundos
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
