<?php
// Versión simplificada para diagnosticar problemas
session_start();

// Configuración básica
$admin_user = 'admin_seguro';
$admin_pass = 'TuPassword_Muy_Seguro_2024!';

$error = '';
$success = false;

// Procesar login
if (isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username === $admin_user && $password === $admin_pass) {
        $_SESSION['authenticated'] = true;
        $_SESSION['login_time'] = time();
        $success = true;
    } else {
        $error = 'Credenciales incorrectas';
    }
}

// Verificar si ya está autenticado
if (isset($_SESSION['authenticated']) && $_SESSION['authenticated']) {
    // Verificar timeout (30 minutos)
    if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
        session_destroy();
        header('Location: index_simple.php');
        exit;
    }
    
    // Mostrar dashboard simple
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sistema Seguro - Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }
            .header { background: #007cba; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
            .btn { background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
            .status { background: #d4edda; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🛡️ Sistema de Seguridad Activo</h1>
                <p>Sesión iniciada: <?php echo date('H:i:s', $_SESSION['login_time']); ?></p>
            </div>
            
            <div class="status">
                <h2>✓ Sistema Funcionando Correctamente</h2>
                <ul>
                    <li>Autenticación: Activa</li>
                    <li>Sesión: Válida</li>
                    <li>PHP: <?php echo phpversion(); ?></li>
                    <li>Servidor: <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Desconocido'; ?></li>
                </ul>
            </div>
            
            <h2>Información del Sistema</h2>
            <p><strong>IP del cliente:</strong> <?php echo $_SERVER['REMOTE_ADDR'] ?? 'Desconocida'; ?></p>
            <p><strong>User Agent:</strong> <?php echo htmlspecialchars($_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido'); ?></p>
            <p><strong>Hora del servidor:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            
            <h2>Archivos de Seguridad</h2>
            <ul>
                <li>.htaccess: <?php echo file_exists('.htaccess') ? '✓ Presente' : '✗ Faltante'; ?></li>
                <li>index.php: <?php echo file_exists('index.php') ? '✓ Presente' : '✗ Faltante'; ?></li>
                <li>dashboard.php: <?php echo file_exists('dashboard.php') ? '✓ Presente' : '✗ Faltante'; ?></li>
            </ul>
            
            <p style="margin-top: 30px;">
                <a href="?logout=1" class="btn">Cerrar Sesión</a>
                <?php if (file_exists('index.php')): ?>
                    <a href="index.php" class="btn" style="background: #007cba;">Ir al Sistema Completo</a>
                <?php endif; ?>
            </p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Procesar logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index_simple.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acceso Seguro - Versión Simple</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #f0f0f0; 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            height: 100vh; 
            margin: 0; 
        }
        .login-container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1); 
            max-width: 400px; 
            width: 100%; 
        }
        .form-group { 
            margin-bottom: 15px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
        }
        input[type="text"], input[type="password"] { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            box-sizing: border-box; 
        }
        button { 
            width: 100%; 
            padding: 12px; 
            background: #007cba; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
        }
        button:hover { 
            background: #005a87; 
        }
        .error { 
            color: red; 
            margin-bottom: 15px; 
            text-align: center; 
        }
        .success { 
            color: green; 
            margin-bottom: 15px; 
            text-align: center; 
        }
        .info { 
            background: #e7f3ff; 
            padding: 15px; 
            border-radius: 5px; 
            margin-bottom: 15px; 
            font-size: 14px; 
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>🛡️ Acceso Seguro</h2>
        <div class="info">
            <strong>Versión de diagnóstico</strong><br>
            Si ves esta página, PHP está funcionando correctamente.
        </div>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <?php if ($success): ?>
            <div class="success">¡Login exitoso! Redirigiendo...</div>
            <script>setTimeout(function(){ window.location.reload(); }, 2000);</script>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Usuario:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Contraseña:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit" name="login">Acceder</button>
        </form>
        
        <p style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            Credenciales por defecto:<br>
            Usuario: admin_seguro<br>
            Contraseña: TuPassword_Muy_Seguro_2024!
        </p>
        
        <p style="text-align: center; margin-top: 15px;">
            <a href="test.php" style="color: #007cba; text-decoration: none;">→ Ejecutar Test de PHP</a>
        </p>
    </div>
</body>
</html>
