# 🌍 Sistema de Control de Acceso Geográfico - RogsMediaTV

## 📋 Descripción
Sistema sencillo pero efectivo que controla el acceso a tu sitio web basado en ubicación geográfica y solicitudes manuales de acceso.

## 🎯 Características Principales

### ✅ Control Geográfico
- **Bloquea automáticamente** acceso desde Europa
- **Permite acceso** desde Latinoamérica y Estados Unidos
- **Detección automática** de país, ISP y ubicación

### 🔐 Panel de Administración
- **Solo accesible desde Panamá**
- **Control total** de solicitudes de acceso
- **Sistema de ban/unban** manual
- **Monitoreo en tiempo real**

### 📝 Sistema de Solicitudes
- Los usuarios pueden **solicitar acceso** con motivo
- **Aprobación manual** por el administrador
- **Acceso total o limitado** por rutas específicas
- **Revocación** de accesos en cualquier momento

## 🚀 Instalación

### 1. Subir Archivos
Sube estos archivos a tu hosting:
- `index.php` - Sistema principal
- `admin_panel.php` - Panel de administración
- `.htaccess` - Configuración del servidor

### 2. Configurar Credenciales
Edita `index.php` y cambia:
```php
define('ADMIN_USER', 'tu_usuario');
define('ADMIN_PASS', 'tu_password_seguro');
```

### 3. Configurar Permisos
- Archivos PHP: 644
- .htaccess: 644
- Directorio: 755

## 🎛️ Uso del Sistema

### Acceso al Panel Admin
1. Ve a: `https://tudominio.com/?admin`
2. **Solo funciona desde Panamá**
3. Usa las credenciales configuradas

### Funciones del Panel
- **📋 Ver solicitudes pendientes**
- **✅ Aprobar/Denegar accesos**
- **🚫 Banear/Desbanear IPs**
- **📊 Ver log de accesos**
- **⚙️ Revocar accesos activos**

### Para Visitantes
1. **Desde Europa**: Acceso bloqueado automáticamente
2. **Desde Latam/USA**: Formulario de solicitud de acceso
3. **Con acceso aprobado**: Contenido completo disponible

## 📁 Archivos Generados Automáticamente

El sistema crea estos archivos para funcionar:
- `accesos.log` - Log de todas las visitas
- `solicitudes_acceso.json` - Solicitudes de acceso
- `accesos_aprobados.json` - Accesos aprobados
- `lista_negra.json` - IPs baneadas

## 🔧 Configuración Avanzada

### Cambiar País del Admin
En `index.php`:
```php
define('PAIS_ADMIN', 'PA'); // Código de país (PA = Panamá)
```

### Añadir Más Países Bloqueados
Edita el array `$paisesEuropaBloqueados` en `index.php`

### Personalizar Rutas Protegidas
El sistema protege automáticamente todas las rutas. Puedes personalizar qué rutas requieren aprobación específica.

## 📊 Logs y Monitoreo

### Información Registrada
- **IP y geolocalización** de cada visitante
- **ISP y organización**
- **Rutas accedidas**
- **User Agent y referer**
- **Estado del acceso** (permitido/bloqueado/solicitud)

### Tipos de Acceso
- `PERMITIDO_APROBADO` - Usuario con acceso aprobado
- `BLOQUEADO_EUROPA` - Bloqueado por ubicación
- `SOLICITUD_ENVIADA` - Solicitud de acceso enviada
- `SOLICITUD_REQUERIDA` - Necesita solicitar acceso

## 🛡️ Seguridad

### Protecciones Incluidas
- **Archivos sensibles protegidos** (.json, .log)
- **Panel admin geográficamente restringido**
- **Control manual de todos los accesos**
- **Sistema de ban permanente**

### Recomendaciones
1. **Cambia las credenciales** por defecto
2. **Revisa regularmente** las solicitudes
3. **Monitorea los logs** de acceso
4. **Mantén backup** de los archivos de configuración

## 🆘 Solución de Problemas

### Panel Admin No Accesible
- Verifica que estés accediendo desde Panamá
- Confirma las credenciales en `index.php`

### Solicitudes No Aparecen
- Verifica permisos de escritura en el directorio
- Revisa que el archivo `solicitudes_acceso.json` se esté creando

### Errores de Geolocalización
- El sistema usa `ip-api.com` (gratuito)
- Si falla, mostrará "Desconocido" pero seguirá funcionando

## 📞 Soporte

Para problemas específicos:
1. Revisa los logs de error de tu hosting
2. Verifica que PHP tenga permisos de escritura
3. Confirma que `file_get_contents()` esté habilitado

---

**Sistema desarrollado para RogsMediaTV**  
*Control de acceso geográfico y solicitudes manuales*
