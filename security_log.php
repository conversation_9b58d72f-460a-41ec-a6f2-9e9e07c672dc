<?php
session_start();

// Verificar autenticación
if (!($_SESSION['authenticated'] ?? false)) {
    header('Location: index.php');
    exit;
}

// Verificar timeout de sesión
if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// ========================================
// FUNCIONES DE LOGGING
// ========================================

function logSecurityEvent($type, $details, $ip = null) {
    $ip = $ip ?: $_SERVER['REMOTE_ADDR'];
    $timestamp = date('Y-m-d H:i:s');
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    
    $logEntry = [
        'timestamp' => $timestamp,
        'ip' => $ip,
        'type' => $type,
        'details' => $details,
        'user_agent' => $userAgent,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'referer' => $_SERVER['HTTP_REFERER'] ?? ''
    ];
    
    $logFile = 'security_events.log';
    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}

function getSecurityLogs($limit = 100) {
    $logFile = 'security_events.log';
    if (!file_exists($logFile)) {
        return [];
    }
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $logs = [];
    
    // Obtener las últimas líneas
    $lines = array_slice($lines, -$limit);
    
    foreach ($lines as $line) {
        $log = json_decode($line, true);
        if ($log) {
            $logs[] = $log;
        }
    }
    
    return array_reverse($logs); // Más recientes primero
}

function analyzeThreats() {
    $logs = getSecurityLogs(1000);
    $analysis = [
        'total_events' => count($logs),
        'unique_ips' => [],
        'attack_types' => [],
        'recent_activity' => 0
    ];
    
    $oneHourAgo = time() - 3600;
    
    foreach ($logs as $log) {
        $ip = $log['ip'];
        $type = $log['type'];
        $timestamp = strtotime($log['timestamp']);
        
        // Contar IPs únicas
        if (!in_array($ip, $analysis['unique_ips'])) {
            $analysis['unique_ips'][] = $ip;
        }
        
        // Contar tipos de ataque
        if (!isset($analysis['attack_types'][$type])) {
            $analysis['attack_types'][$type] = 0;
        }
        $analysis['attack_types'][$type]++;
        
        // Actividad reciente
        if ($timestamp > $oneHourAgo) {
            $analysis['recent_activity']++;
        }
    }
    
    $analysis['unique_ips'] = count($analysis['unique_ips']);
    return $analysis;
}

// Procesar acciones
if ($_POST['action'] ?? false) {
    switch ($_POST['action']) {
        case 'clear_logs':
            if (file_exists('security_events.log')) {
                unlink('security_events.log');
                $message = 'Logs de seguridad limpiados exitosamente.';
            }
            break;
        case 'test_log':
            logSecurityEvent('test', 'Evento de prueba generado desde el panel', $_SERVER['REMOTE_ADDR']);
            $message = 'Evento de prueba registrado.';
            break;
    }
}

$logs = getSecurityLogs(50);
$analysis = analyzeThreats();

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log de Seguridad - Sistema Seguro</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .header { 
            background: #dc3545; 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .card { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px; 
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 20px; 
            margin-bottom: 20px; 
        }
        .stat-card { 
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
        }
        .stat-number { 
            font-size: 2em; 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px; 
            font-size: 12px; 
        }
        th, td { 
            padding: 8px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
        }
        th { 
            background: #f8f9fa; 
            font-weight: bold; 
            position: sticky; 
            top: 0; 
        }
        .btn { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 5px; 
            display: inline-block; 
            margin: 5px; 
            border: none; 
            cursor: pointer; 
        }
        .btn:hover { 
            background: #005a87; 
        }
        .btn-danger { 
            background: #dc3545; 
        }
        .btn-danger:hover { 
            background: #c82333; 
        }
        .alert { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .log-entry { 
            margin-bottom: 10px; 
            padding: 10px; 
            border-left: 4px solid #dc3545; 
            background: #f8f9fa; 
        }
        .log-timestamp { 
            font-weight: bold; 
            color: #666; 
        }
        .log-type { 
            background: #dc3545; 
            color: white; 
            padding: 2px 8px; 
            border-radius: 3px; 
            font-size: 11px; 
            margin-left: 10px; 
        }
        .log-details { 
            margin-top: 5px; 
            font-family: monospace; 
            background: white; 
            padding: 5px; 
            border-radius: 3px; 
        }
        .threat-high { background: #dc3545; }
        .threat-medium { background: #ffc107; }
        .threat-low { background: #28a745; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ Log de Seguridad</h1>
        <div>
            <a href="dashboard.php" class="btn">← Volver al Dashboard</a>
        </div>
    </div>

    <?php if (isset($message)): ?>
        <div class="alert"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number"><?php echo $analysis['total_events']; ?></div>
            <div>Eventos Totales</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $analysis['unique_ips']; ?></div>
            <div>IPs Únicas</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo $analysis['recent_activity']; ?></div>
            <div>Eventos (última hora)</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo count($analysis['attack_types']); ?></div>
            <div>Tipos de Amenaza</div>
        </div>
    </div>

    <div class="card">
        <h2>Tipos de Amenazas Detectadas</h2>
        <?php if (empty($analysis['attack_types'])): ?>
            <p style="color: #28a745;">✓ No se han detectado amenazas</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>Tipo de Amenaza</th>
                        <th>Cantidad</th>
                        <th>Porcentaje</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($analysis['attack_types'] as $type => $count): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($type); ?></td>
                            <td><?php echo $count; ?></td>
                            <td><?php echo round(($count / $analysis['total_events']) * 100, 1); ?>%</td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2>Eventos Recientes (últimos 50)</h2>
        
        <div style="margin-bottom: 15px;">
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="test_log">
                <button type="submit" class="btn">Generar Evento de Prueba</button>
            </form>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('¿Estás seguro de que quieres limpiar todos los logs?');">
                <input type="hidden" name="action" value="clear_logs">
                <button type="submit" class="btn btn-danger">Limpiar Logs</button>
            </form>
        </div>

        <?php if (empty($logs)): ?>
            <p style="color: #28a745;">✓ No hay eventos de seguridad registrados</p>
        <?php else: ?>
            <div style="max-height: 600px; overflow-y: auto;">
                <?php foreach ($logs as $log): ?>
                    <div class="log-entry">
                        <div>
                            <span class="log-timestamp"><?php echo htmlspecialchars($log['timestamp']); ?></span>
                            <span class="log-type"><?php echo htmlspecialchars($log['type']); ?></span>
                            <strong>IP:</strong> <?php echo htmlspecialchars($log['ip']); ?>
                        </div>
                        <div class="log-details">
                            <strong>Detalles:</strong> <?php echo htmlspecialchars($log['details']); ?><br>
                            <strong>User Agent:</strong> <?php echo htmlspecialchars(substr($log['user_agent'], 0, 100)); ?><br>
                            <?php if (!empty($log['request_uri'])): ?>
                                <strong>URI:</strong> <?php echo htmlspecialchars($log['request_uri']); ?><br>
                            <?php endif; ?>
                            <?php if (!empty($log['referer'])): ?>
                                <strong>Referer:</strong> <?php echo htmlspecialchars($log['referer']); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Auto-refresh cada 60 segundos
        setTimeout(function() {
            window.location.reload();
        }, 60000);
    </script>
</body>
</html>
