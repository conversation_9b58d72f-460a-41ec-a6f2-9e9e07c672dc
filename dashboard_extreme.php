<?php
session_start();

// Verificar autenticación extrema
if (!($_SESSION['authenticated'] ?? false) || ($_SESSION['admin_level'] ?? '') !== 'extreme') {
    header('Location: index_extreme.php');
    exit;
}

// Verificar timeout de sesión (30 minutos)
if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
    session_destroy();
    header('Location: index_extreme.php');
    exit;
}

// Procesar logout
if ($_GET['logout'] ?? false) {
    session_destroy();
    header('Location: index_extreme.php');
    exit;
}

// ========================================
// FUNCIONES DE ANÁLISIS FORENSE
// ========================================

function getSecurityStats() {
    $stats = [
        'total_threats' => 0,
        'blocked_ips' => 0,
        'honeypot_triggers' => 0,
        'brute_force_attempts' => 0,
        'rate_limit_violations' => 0,
        'permanent_bans' => 0,
        'active_sessions' => 1,
        'last_attack' => 'Nunca'
    ];
    
    // Analizar logs de seguridad
    if (file_exists('security_extreme.log')) {
        $logs = file('security_extreme.log', FILE_IGNORE_NEW_LINES);
        $stats['total_threats'] = count($logs);
        
        $oneHourAgo = time() - 3600;
        foreach ($logs as $log) {
            $data = json_decode($log, true);
            if ($data && strtotime($data['timestamp']) > $oneHourAgo) {
                $stats['last_attack'] = $data['timestamp'];
                break;
            }
        }
    }
    
    // Contar IPs bloqueadas
    $banFiles = glob('honeypot_ban_*.txt');
    foreach ($banFiles as $file) {
        $banTime = (int)file_get_contents($file);
        if (time() < $banTime) {
            $stats['blocked_ips']++;
        }
    }
    
    // Contar bans permanentes
    $bruteFiles = glob('brute_force_*.json');
    foreach ($bruteFiles as $file) {
        $data = json_decode(file_get_contents($file), true);
        if ($data['permanent_ban'] ?? false) {
            $stats['permanent_bans']++;
        }
    }
    
    return $stats;
}

function getThreatAnalysis() {
    $threats = [];
    $threatTypes = [];
    
    if (file_exists('security_extreme.log')) {
        $logs = file('security_extreme.log', FILE_IGNORE_NEW_LINES);
        $recentLogs = array_slice($logs, -100); // Últimos 100 eventos
        
        foreach ($recentLogs as $log) {
            $data = json_decode($log, true);
            if ($data) {
                $threats[] = $data;
                $type = $data['type'];
                $threatTypes[$type] = ($threatTypes[$type] ?? 0) + 1;
            }
        }
    }
    
    return ['events' => array_reverse($threats), 'types' => $threatTypes];
}

function getTopAttackers() {
    $attackers = [];
    
    if (file_exists('security_extreme.log')) {
        $logs = file('security_extreme.log', FILE_IGNORE_NEW_LINES);
        
        foreach ($logs as $log) {
            $data = json_decode($log, true);
            if ($data && $data['severity'] !== 'INFO') {
                $ip = $data['ip_hash'];
                if (!isset($attackers[$ip])) {
                    $attackers[$ip] = [
                        'count' => 0,
                        'last_attack' => $data['timestamp'],
                        'types' => [],
                        'severity' => 'LOW'
                    ];
                }
                $attackers[$ip]['count']++;
                $attackers[$ip]['types'][] = $data['type'];
                if ($data['severity'] === 'CRITICAL') {
                    $attackers[$ip]['severity'] = 'CRITICAL';
                } elseif ($data['severity'] === 'HIGH' && $attackers[$ip]['severity'] !== 'CRITICAL') {
                    $attackers[$ip]['severity'] = 'HIGH';
                }
            }
        }
    }
    
    // Ordenar por número de ataques
    uasort($attackers, function($a, $b) {
        return $b['count'] - $a['count'];
    });
    
    return array_slice($attackers, 0, 10, true); // Top 10
}

function cleanupOldFiles() {
    $cleaned = 0;
    $files = array_merge(
        glob('rate_extreme_*.json'),
        glob('brute_force_*.json'),
        glob('honeypot_ban_*.txt')
    );
    
    foreach ($files as $file) {
        if (filemtime($file) < time() - 86400) { // Más de 24 horas
            unlink($file);
            $cleaned++;
        }
    }
    
    return $cleaned;
}

// Procesar acciones
$message = '';
if ($_POST['action'] ?? false) {
    switch ($_POST['action']) {
        case 'cleanup':
            $cleaned = cleanupOldFiles();
            $message = "Se limpiaron $cleaned archivos antiguos.";
            break;
        case 'clear_logs':
            if (file_exists('security_extreme.log')) {
                unlink('security_extreme.log');
                $message = 'Logs de seguridad limpiados.';
            }
            break;
        case 'emergency_lockdown':
            // Crear archivo de bloqueo de emergencia
            file_put_contents('emergency_lockdown.flag', time());
            $message = 'Modo de bloqueo de emergencia activado.';
            break;
    }
}

$stats = getSecurityStats();
$threatAnalysis = getThreatAnalysis();
$topAttackers = getTopAttackers();

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Centro de Comando de Seguridad Extrema</title>
    <style>
        * { box-sizing: border-box; }
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0; 
            padding: 0; 
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #fff;
            min-height: 100vh;
        }
        .header { 
            background: rgba(0,0,0,0.3);
            backdrop-filter: blur(10px);
            padding: 20px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .container {
            padding: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card { 
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            padding: 25px; 
            border-radius: 15px; 
            border: 1px solid rgba(255,255,255,0.2);
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        .stat-card {
            text-align: center;
            background: linear-gradient(135deg, rgba(255,107,107,0.2) 0%, rgba(238,90,36,0.2) 100%);
        }
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .stat-label {
            font-size: 14px;
            opacity: 0.9;
        }
        .threat-high { color: #ff6b6b; }
        .threat-medium { color: #feca57; }
        .threat-low { color: #48dbfb; }
        .threat-critical { 
            color: #ff3838; 
            animation: pulse 1s infinite;
            font-weight: bold;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .btn { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 8px; 
            display: inline-block; 
            margin: 5px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover { 
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }
        .btn-danger { 
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        }
        .alert { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 8px; 
            background: rgba(72, 219, 251, 0.2);
            border-left: 4px solid #48dbfb;
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px;
            background: rgba(0,0,0,0.2);
            border-radius: 8px;
            overflow: hidden;
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        th { 
            background: rgba(0,0,0,0.3);
            font-weight: bold;
        }
        .log-entry {
            background: rgba(0,0,0,0.2);
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ff6b6b;
        }
        .log-timestamp {
            font-size: 12px;
            opacity: 0.7;
        }
        .log-type {
            background: rgba(255,107,107,0.3);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            margin-left: 10px;
        }
        .security-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .status-item {
            background: rgba(0,0,0,0.2);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-active { border-left: 4px solid #48dbfb; }
        .status-warning { border-left: 4px solid #feca57; }
        .status-critical { border-left: 4px solid #ff6b6b; }
        .real-time-monitor {
            background: rgba(0,0,0,0.3);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .monitor-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        .monitor-item:last-child {
            border-bottom: none;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #48dbfb;
            animation: blink 2s infinite;
        }
        .status-indicator.critical {
            background: #ff6b6b;
            animation: blink 0.5s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ CENTRO DE COMANDO - SEGURIDAD EXTREMA</h1>
        <div>
            <span>Sesión: <?php echo date('H:i:s', $_SESSION['login_time']); ?></span>
            <a href="?logout=1" class="btn btn-danger">🚪 Salir</a>
        </div>
    </div>

    <div class="container">
        <?php if ($message): ?>
            <div class="alert">✅ <?php echo htmlspecialchars($message); ?></div>
        <?php endif; ?>

        <div class="real-time-monitor">
            <h2>📡 Monitor en Tiempo Real</h2>
            <div class="monitor-item">
                <span>🛡️ Sistema de Protección</span>
                <div class="status-indicator"></div>
            </div>
            <div class="monitor-item">
                <span>🕷️ Detección de Bots</span>
                <div class="status-indicator"></div>
            </div>
            <div class="monitor-item">
                <span>🍯 Sistema Honeypot</span>
                <div class="status-indicator"></div>
            </div>
            <div class="monitor-item">
                <span>⚡ Rate Limiting</span>
                <div class="status-indicator"></div>
            </div>
            <div class="monitor-item">
                <span>🔥 Análisis de Amenazas</span>
                <div class="status-indicator critical"></div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card stat-card">
                <div class="stat-number threat-critical"><?php echo $stats['total_threats']; ?></div>
                <div class="stat-label">Amenazas Detectadas</div>
            </div>
            <div class="card stat-card">
                <div class="stat-number threat-high"><?php echo $stats['blocked_ips']; ?></div>
                <div class="stat-label">IPs Bloqueadas</div>
            </div>
            <div class="card stat-card">
                <div class="stat-number threat-medium"><?php echo $stats['permanent_bans']; ?></div>
                <div class="stat-label">Bans Permanentes</div>
            </div>
            <div class="card stat-card">
                <div class="stat-number threat-low"><?php echo $stats['active_sessions']; ?></div>
                <div class="stat-label">Sesiones Activas</div>
            </div>
        </div>

        <div class="card">
            <h2>🎯 Top Atacantes</h2>
            <?php if (empty($topAttackers)): ?>
                <p class="threat-low">✅ No hay atacantes detectados</p>
            <?php else: ?>
                <table>
                    <thead>
                        <tr>
                            <th>IP (Hash)</th>
                            <th>Ataques</th>
                            <th>Último Ataque</th>
                            <th>Severidad</th>
                            <th>Tipos</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($topAttackers as $hash => $data): ?>
                            <tr>
                                <td><?php echo substr($hash, 0, 16) . '...'; ?></td>
                                <td><span class="threat-critical"><?php echo $data['count']; ?></span></td>
                                <td><?php echo $data['last_attack']; ?></td>
                                <td><span class="threat-<?php echo strtolower($data['severity']); ?>"><?php echo $data['severity']; ?></span></td>
                                <td><?php echo count(array_unique($data['types'])); ?> tipos</td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h2>⚡ Acciones de Emergencia</h2>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="cleanup">
                <button type="submit" class="btn">🧹 Limpiar Archivos</button>
            </form>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('¿Limpiar todos los logs?');">
                <input type="hidden" name="action" value="clear_logs">
                <button type="submit" class="btn btn-warning">🗑️ Limpiar Logs</button>
            </form>
            
            <form method="POST" style="display: inline;" onsubmit="return confirm('¿Activar bloqueo de emergencia?');">
                <input type="hidden" name="action" value="emergency_lockdown">
                <button type="submit" class="btn btn-danger">🚨 Bloqueo de Emergencia</button>
            </form>
        </div>

        <div class="card">
            <h2>📊 Información del Sistema</h2>
            <div class="security-status">
                <div class="status-item status-active">
                    <strong>Última Amenaza</strong><br>
                    <?php echo $stats['last_attack']; ?>
                </div>
                <div class="status-item status-active">
                    <strong>Tiempo Activo</strong><br>
                    <?php echo gmdate("H:i:s", time() - $_SESSION['login_time']); ?>
                </div>
                <div class="status-item status-active">
                    <strong>Nivel de Alerta</strong><br>
                    <span class="threat-critical">EXTREMO</span>
                </div>
                <div class="status-item status-active">
                    <strong>Estado</strong><br>
                    🟢 OPERATIVO
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh cada 15 segundos
        setTimeout(function() {
            window.location.reload();
        }, 15000);
        
        // Efectos visuales para indicadores
        document.querySelectorAll('.status-indicator').forEach(indicator => {
            setInterval(() => {
                indicator.style.boxShadow = indicator.classList.contains('critical') 
                    ? '0 0 20px #ff6b6b' 
                    : '0 0 15px #48dbfb';
                setTimeout(() => {
                    indicator.style.boxShadow = 'none';
                }, 200);
            }, 2000);
        });
    </script>
</body>
</html>
