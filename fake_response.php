<?php
// ========================================
// RESPUESTAS FALSAS PARA CONFUNDIR SCANNERS
// ========================================

// Log del intento de acceso a archivos comunes
$ip = $_SERVER['REMOTE_ADDR'];
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
$requestUri = $_SERVER['REQUEST_URI'] ?? '';

$logEntry = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $ip,
    'ip_hash' => hash('sha256', $ip),
    'type' => 'SCANNER_DETECTION',
    'severity' => 'HIGH',
    'details' => "Intento de acceso a archivo común: $requestUri",
    'user_agent' => $userAgent,
    'request_uri' => $requestUri
];

file_put_contents('security_extreme.log', json_encode($logEntry) . "\n", FILE_APPEND | LOCK_EX);

// Determinar qué tipo de respuesta falsa enviar
$uri = strtolower($requestUri);

if (strpos($uri, 'robots.txt') !== false) {
    // Robots.txt falso que confunde a los bots
    header('Content-Type: text/plain');
    echo "User-agent: *\n";
    echo "Disallow: /admin/\n";
    echo "Disallow: /wp-admin/\n";
    echo "Disallow: /private/\n";
    echo "Disallow: /secret/\n";
    echo "Disallow: /backup/\n";
    echo "Allow: /honeypot/\n";
    echo "Allow: /fake-login/\n";
    echo "Crawl-delay: 86400\n";
    echo "# Fake entries to waste scanner time\n";
    for ($i = 1; $i <= 50; $i++) {
        echo "Disallow: /fake-dir-$i/\n";
    }

} elseif (strpos($uri, 'sitemap.xml') !== false) {
    // Sitemap falso con URLs honeypot
    header('Content-Type: application/xml');
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

    // URLs falsas para hacer perder tiempo a los scanners
    $fakeUrls = [
        '/admin-panel/', '/wp-admin/', '/administrator/', '/cpanel/',
        '/phpmyadmin/', '/webmail/', '/ftp/', '/ssh/', '/telnet/',
        '/backup/', '/database/', '/config/', '/private/', '/secret/',
        '/api/v1/users/', '/api/v2/admin/', '/rest/admin/', '/json/users/',
        '/login.php', '/admin.php', '/panel.php', '/control.php'
    ];

    foreach ($fakeUrls as $url) {
        echo "  <url>\n";
        echo "    <loc>https://" . $_SERVER['HTTP_HOST'] . $url . "</loc>\n";
        echo "    <lastmod>" . date('Y-m-d') . "</lastmod>\n";
        echo "    <changefreq>daily</changefreq>\n";
        echo "    <priority>0.1</priority>\n";
        echo "  </url>\n";
    }

    echo '</urlset>';

} elseif (strpos($uri, 'favicon.ico') !== false) {
    // Favicon falso que toma tiempo en cargar
    header('Content-Type: image/x-icon');
    header('Content-Length: 1024');

    // Generar datos aleatorios para simular un favicon
    echo str_repeat(chr(0), 1024);

} else {
    // Respuesta genérica que simula un error 404 pero registra el intento
    http_response_code(404);
    header('Content-Type: text/html');

    echo '<!DOCTYPE html>';
    echo '<html><head><title>404 Not Found</title></head>';
    echo '<body><h1>Not Found</h1>';
    echo '<p>The requested URL was not found on this server.</p>';
    echo '<hr><address>Apache/2.4.41 (Ubuntu) Server at ' . $_SERVER['HTTP_HOST'] . ' Port 443</address>';
    echo '</body></html>';
}

// Añadir delay aleatorio para hacer perder tiempo a los scanners
usleep(rand(100000, 500000)); // 0.1 a 0.5 segundos
?>