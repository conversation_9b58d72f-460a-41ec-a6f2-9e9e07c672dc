<?php
// Archivo de prueba para diagnosticar problemas
echo "<!DOCTYPE html>";
echo "<html><head><title>Test PHP</title></head><body>";
echo "<h1>PHP está funcionando!</h1>";
echo "<p>Fe<PERSON> y hora: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Versión de PHP: " . phpversion() . "</p>";
echo "<p>Servidor: " . $_SERVER['SERVER_SOFTWARE'] . "</p>";

// Verificar si las funciones necesarias están disponibles
echo "<h2>Verificación de funciones:</h2>";
echo "<ul>";
echo "<li>session_start: " . (function_exists('session_start') ? '✓ Disponible' : '✗ No disponible') . "</li>";
echo "<li>file_get_contents: " . (function_exists('file_get_contents') ? '✓ Disponible' : '✗ No disponible') . "</li>";
echo "<li>file_put_contents: " . (function_exists('file_put_contents') ? '✓ Disponible' : '✗ No disponible') . "</li>";
echo "<li>json_encode: " . (function_exists('json_encode') ? '✓ Disponible' : '✗ No disponible') . "</li>";
echo "<li>json_decode: " . (function_exists('json_decode') ? '✓ Disponible' : '✗ No disponible') . "</li>";
echo "</ul>";

// Verificar permisos de escritura
echo "<h2>Verificación de permisos:</h2>";
$testFile = 'test_write.txt';
if (file_put_contents($testFile, 'test')) {
    echo "<p>✓ Permisos de escritura: OK</p>";
    unlink($testFile);
} else {
    echo "<p>✗ Permisos de escritura: ERROR</p>";
}

// Mostrar errores de PHP si los hay
echo "<h2>Configuración de errores:</h2>";
echo "<p>display_errors: " . ini_get('display_errors') . "</p>";
echo "<p>error_reporting: " . error_reporting() . "</p>";

echo "</body></html>";
?>
