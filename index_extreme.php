<?php
// ========================================
// SISTEMA DE SEGURIDAD EXTREMO - NIVEL MILITAR
// ========================================

// Configuración de seguridad PHP máxima
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');
ini_set('session.gc_maxlifetime', 1800);

// Headers de seguridad extremos
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');
header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\'; style-src \'self\' \'unsafe-inline\'; img-src \'self\' data:; connect-src \'self\'; font-src \'self\'; object-src \'none\'; media-src \'self\'; frame-src \'none\';');
header('Referrer-Policy: strict-origin-when-cross-origin');
header('Permissions-Policy: geolocation=(), microphone=(), camera=()');

// Iniciar sesión segura
session_start();

// Regenerar ID de sesión para prevenir session fixation
if (!isset($_SESSION['initiated'])) {
    session_regenerate_id(true);
    $_SESSION['initiated'] = true;
    $_SESSION['creation_time'] = time();
    $_SESSION['fingerprint'] = hash('sha256', $_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
}

// Verificar fingerprint de sesión
$current_fingerprint = hash('sha256', $_SERVER['HTTP_USER_AGENT'] . $_SERVER['REMOTE_ADDR']);
if ($_SESSION['fingerprint'] !== $current_fingerprint) {
    session_destroy();
    die('Sesión comprometida detectada');
}

// ========================================
// CONFIGURACIÓN DE SEGURIDAD EXTREMA
// ========================================

define('ADMIN_USERNAME', 'admin_seguro');
define('ADMIN_PASSWORD', 'TuPassword_Muy_Seguro_2024!');
define('MAX_LOGIN_ATTEMPTS', 2); // Solo 2 intentos
define('LOCKOUT_TIME', 7200); // 2 horas de bloqueo
define('HONEYPOT_LOCKOUT', 86400); // 24 horas si cae en honeypot
define('BRUTE_FORCE_THRESHOLD', 5); // Bloqueo permanente después de 5 intentos totales
define('SECURITY_TOKEN_LENGTH', 64);
define('RATE_LIMIT_REQUESTS', 5); // Máximo 5 requests por minuto
define('RATE_LIMIT_WINDOW', 60); // Ventana de 1 minuto

// ========================================
// SISTEMA DE DETECCIÓN AVANZADO
// ========================================

function detectAdvancedThreats() {
    $threats = [];
    $ip = $_SERVER['REMOTE_ADDR'];
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';
    $queryString = $_SERVER['QUERY_STRING'] ?? '';
    
    // Detectar herramientas de hacking avanzadas
    $hackingTools = [
        'sqlmap', 'nmap', 'masscan', 'zmap', 'nuclei', 'gobuster', 'dirb', 'dirbuster',
        'burp', 'owasp', 'w3af', 'skipfish', 'wpscan', 'joomscan', 'droopescan',
        'acunetix', 'nessus', 'openvas', 'qualys', 'rapid7', 'metasploit',
        'havij', 'pangolin', 'bbqsql', 'bsqlbf', 'marathon', 'sqlninja',
        'hydra', 'medusa', 'brutus', 'ncrack', 'patator', 'crowbar',
        'beef', 'xsser', 'commix', 'sqliv', 'fimap', 'uniscan',
        'whatweb', 'wapiti', 'arachni', 'vega', 'grendel', 'paros',
        'appscan', 'webinspect', 'hailstorm', 'webscarab', 'proxystrike'
    ];
    
    foreach ($hackingTools as $tool) {
        if (stripos($userAgent, $tool) !== false) {
            $threats[] = "Herramienta de hacking detectada: $tool";
        }
    }
    
    // Detectar patrones de inyección SQL avanzados
    $sqlPatterns = [
        '/union.*select/i', '/select.*from/i', '/insert.*into/i', '/update.*set/i',
        '/delete.*from/i', '/drop.*table/i', '/create.*table/i', '/alter.*table/i',
        '/exec.*\(/i', '/system.*\(/i', '/shell_exec/i', '/passthru/i',
        '/benchmark.*\(/i', '/sleep.*\(/i', '/waitfor.*delay/i', '/pg_sleep/i',
        '/information_schema/i', '/mysql\.user/i', '/pg_user/i', '/sys\.objects/i',
        '/load_file/i', '/into.*outfile/i', '/into.*dumpfile/i',
        '/0x[0-9a-f]+/i', '/char\(/i', '/ascii\(/i', '/ord\(/i', '/hex\(/i'
    ];
    
    foreach ($sqlPatterns as $pattern) {
        if (preg_match($pattern, $queryString) || preg_match($pattern, $requestUri)) {
            $threats[] = "Patrón de inyección SQL detectado";
            break;
        }
    }
    
    // Detectar XSS avanzado
    $xssPatterns = [
        '/<script/i', '/<iframe/i', '/<object/i', '/<embed/i', '/<applet/i',
        '/javascript:/i', '/vbscript:/i', '/data:/i', '/about:/i',
        '/onload.*=/i', '/onerror.*=/i', '/onclick.*=/i', '/onmouseover.*=/i',
        '/alert\(/i', '/confirm\(/i', '/prompt\(/i', '/document\./i', '/window\./i'
    ];
    
    foreach ($xssPatterns as $pattern) {
        if (preg_match($pattern, $queryString) || preg_match($pattern, $requestUri)) {
            $threats[] = "Patrón XSS detectado";
            break;
        }
    }
    
    // Detectar directory traversal
    if (preg_match('/(\.\.|%2e%2e|%252e%252e)/i', $requestUri)) {
        $threats[] = "Directory traversal detectado";
    }
    
    // Detectar command injection
    $cmdPatterns = ['/\||;|&|`|\$\(|\${|<\(|>\(/i', '/(nc |netcat|telnet|wget |curl |lynx)/i'];
    foreach ($cmdPatterns as $pattern) {
        if (preg_match($pattern, $queryString)) {
            $threats[] = "Command injection detectado";
            break;
        }
    }
    
    // Detectar user agents sospechosos
    if (empty($userAgent) || strlen($userAgent) < 10) {
        $threats[] = "User agent sospechoso o vacío";
    }
    
    // Detectar requests sin referer (posible bot)
    if (empty($_SERVER['HTTP_REFERER']) && !empty($queryString)) {
        $threats[] = "Request sin referer con parámetros";
    }
    
    return $threats;
}

// ========================================
// SISTEMA DE HONEYPOT AVANZADO
// ========================================

function createHoneypot() {
    // Campo honeypot invisible para bots
    return '<input type="text" name="website" style="display:none !important;" tabindex="-1" autocomplete="off">';
}

function checkHoneypot() {
    return !empty($_POST['website']);
}

// ========================================
// SISTEMA DE RATE LIMITING EXTREMO
// ========================================

function enforceRateLimit() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'rate_extreme_' . hash('sha256', $ip) . '.json';
    $now = time();
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        
        // Limpiar requests antiguos
        $data['requests'] = array_filter($data['requests'], function($timestamp) use ($now) {
            return ($now - $timestamp) < RATE_LIMIT_WINDOW;
        });
        
        // Verificar límite
        if (count($data['requests']) >= RATE_LIMIT_REQUESTS) {
            // Incrementar contador de violaciones
            $data['violations'] = ($data['violations'] ?? 0) + 1;
            
            // Si hay muchas violaciones, bloqueo permanente
            if ($data['violations'] > 10) {
                $data['permanent_ban'] = true;
            }
            
            file_put_contents($file, json_encode($data));
            
            if ($data['permanent_ban'] ?? false) {
                logSecurityEvent('PERMANENT_BAN', "IP bloqueada permanentemente por rate limiting extremo", $ip);
                die('IP bloqueada permanentemente');
            }
            
            logSecurityEvent('RATE_LIMIT_EXCEEDED', "Rate limit excedido - violación #" . $data['violations'], $ip);
            http_response_code(429);
            die('Rate limit excedido. Intenta más tarde.');
        }
        
        $data['requests'][] = $now;
    } else {
        $data = ['requests' => [$now], 'violations' => 0];
    }
    
    file_put_contents($file, json_encode($data));
}

// ========================================
// SISTEMA DE LOGGING AVANZADO
// ========================================

function logSecurityEvent($type, $details, $ip = null, $severity = 'HIGH') {
    $ip = $ip ?: $_SERVER['REMOTE_ADDR'];
    $timestamp = date('Y-m-d H:i:s');
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
    
    $logEntry = [
        'timestamp' => $timestamp,
        'ip' => $ip,
        'ip_hash' => hash('sha256', $ip),
        'type' => $type,
        'severity' => $severity,
        'details' => $details,
        'user_agent' => $userAgent,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
        'query_string' => $_SERVER['QUERY_STRING'] ?? '',
        'referer' => $_SERVER['HTTP_REFERER'] ?? '',
        'method' => $_SERVER['REQUEST_METHOD'] ?? '',
        'headers' => getallheaders(),
        'geolocation' => getGeoLocation($ip)
    ];
    
    $logFile = 'security_extreme.log';
    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    
    // Si es un ataque severo, notificar inmediatamente
    if ($severity === 'CRITICAL') {
        // Aquí podrías enviar un email o notificación
        file_put_contents('critical_alerts.log', $logLine, FILE_APPEND | LOCK_EX);
    }
}

function getGeoLocation($ip) {
    // Simulación de geolocalización (en producción usarías una API real)
    return ['country' => 'Unknown', 'city' => 'Unknown'];
}

// ========================================
// VERIFICACIONES DE SEGURIDAD EXTREMAS
// ========================================

// Verificar amenazas avanzadas
$threats = detectAdvancedThreats();
if (!empty($threats)) {
    foreach ($threats as $threat) {
        logSecurityEvent('ADVANCED_THREAT', $threat, null, 'CRITICAL');
    }
    http_response_code(403);
    die('Amenaza de seguridad detectada');
}

// Aplicar rate limiting extremo
enforceRateLimit();

// Verificar honeypot
if ($_POST && checkHoneypot()) {
    logSecurityEvent('HONEYPOT_TRIGGERED', 'Bot detectado por honeypot', null, 'CRITICAL');
    
    // Bloquear IP por 24 horas
    $ip = $_SERVER['REMOTE_ADDR'];
    $banFile = 'honeypot_ban_' . hash('sha256', $ip) . '.txt';
    file_put_contents($banFile, time() + HONEYPOT_LOCKOUT);
    
    http_response_code(403);
    die('Acceso denegado');
}

// Verificar si la IP está baneada por honeypot
$ip = $_SERVER['REMOTE_ADDR'];
$banFile = 'honeypot_ban_' . hash('sha256', $ip) . '.txt';
if (file_exists($banFile)) {
    $banTime = (int)file_get_contents($banFile);
    if (time() < $banTime) {
        logSecurityEvent('HONEYPOT_BAN_ACTIVE', 'Intento de acceso con IP baneada por honeypot', null, 'HIGH');
        http_response_code(403);
        die('IP bloqueada por actividad maliciosa');
    } else {
        unlink($banFile); // Eliminar ban expirado
    }
}

// ========================================
// SISTEMA DE AUTENTICACIÓN EXTREMO
// ========================================

function checkBruteForce() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'brute_force_' . hash('sha256', $ip) . '.json';

    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);

        // Verificar si está permanentemente baneado
        if ($data['permanent_ban'] ?? false) {
            logSecurityEvent('PERMANENT_BAN_ATTEMPT', 'Intento de acceso con IP permanentemente baneada', null, 'CRITICAL');
            die('IP bloqueada permanentemente por ataques de fuerza bruta');
        }

        // Verificar bloqueo temporal
        if ($data['locked_until'] > time()) {
            $remaining = $data['locked_until'] - time();
            logSecurityEvent('LOCKOUT_ATTEMPT', "Intento durante bloqueo, quedan $remaining segundos", null, 'HIGH');
            die("IP bloqueada. Tiempo restante: " . gmdate("H:i:s", $remaining));
        }

        // Verificar si excede el umbral de fuerza bruta
        if ($data['total_attempts'] >= BRUTE_FORCE_THRESHOLD) {
            $data['permanent_ban'] = true;
            file_put_contents($file, json_encode($data));
            logSecurityEvent('BRUTE_FORCE_BAN', 'IP baneada permanentemente por fuerza bruta', null, 'CRITICAL');
            die('IP bloqueada permanentemente por múltiples intentos de fuerza bruta');
        }

        if ($data['attempts'] >= MAX_LOGIN_ATTEMPTS) {
            $data['locked_until'] = time() + LOCKOUT_TIME;
            $data['lockout_count'] = ($data['lockout_count'] ?? 0) + 1;
            file_put_contents($file, json_encode($data));
            return false;
        }
    }

    return true;
}

function recordLoginAttempt($success = false) {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'brute_force_' . hash('sha256', $ip) . '.json';

    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
    } else {
        $data = ['attempts' => 0, 'total_attempts' => 0, 'locked_until' => 0, 'lockout_count' => 0];
    }

    if ($success) {
        $data['attempts'] = 0;
        $data['locked_until'] = 0;
        logSecurityEvent('LOGIN_SUCCESS', 'Login exitoso', null, 'INFO');
    } else {
        $data['attempts']++;
        $data['total_attempts']++;
        logSecurityEvent('LOGIN_FAILED', "Intento fallido #{$data['attempts']}, total: {$data['total_attempts']}", null, 'MEDIUM');
    }

    file_put_contents($file, json_encode($data));
}

function generateSecurityToken() {
    return bin2hex(random_bytes(SECURITY_TOKEN_LENGTH));
}

function verifySecurityToken($token) {
    return isset($_SESSION['security_token']) && hash_equals($_SESSION['security_token'], $token);
}

// ========================================
// CAPTCHA MATEMÁTICO AVANZADO
// ========================================

function generateAdvancedCaptcha() {
    $operations = ['+', '-', '*'];
    $operation = $operations[array_rand($operations)];

    switch ($operation) {
        case '+':
            $num1 = rand(10, 50);
            $num2 = rand(10, 50);
            $result = $num1 + $num2;
            break;
        case '-':
            $num1 = rand(20, 100);
            $num2 = rand(1, $num1 - 1);
            $result = $num1 - $num2;
            break;
        case '*':
            $num1 = rand(2, 12);
            $num2 = rand(2, 12);
            $result = $num1 * $num2;
            break;
    }

    $_SESSION['captcha_result'] = $result;
    $_SESSION['captcha_time'] = time();

    return "$num1 $operation $num2";
}

function verifyCaptcha($answer) {
    // Verificar que el captcha no sea muy viejo (máximo 5 minutos)
    if (!isset($_SESSION['captcha_time']) || (time() - $_SESSION['captcha_time']) > 300) {
        return false;
    }

    return isset($_SESSION['captcha_result']) && (int)$answer === $_SESSION['captcha_result'];
}

// ========================================
// PROCESAMIENTO PRINCIPAL
// ========================================

// Verificar intentos de fuerza bruta
if (!checkBruteForce()) {
    http_response_code(423);
    die('IP bloqueada por múltiples intentos fallidos');
}

// Generar token de seguridad
if (!isset($_SESSION['security_token'])) {
    $_SESSION['security_token'] = generateSecurityToken();
}

// Generar captcha
$captcha_question = generateAdvancedCaptcha();

$error = '';
$showExtraVerification = false;

if ($_POST['login'] ?? false) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    $token = $_POST['security_token'] ?? '';

    // Verificar token de seguridad
    if (!verifySecurityToken($token)) {
        logSecurityEvent('CSRF_ATTEMPT', 'Token de seguridad inválido', null, 'HIGH');
        $error = 'Token de seguridad inválido';
        recordLoginAttempt(false);
    }
    // Verificar CAPTCHA
    elseif (!verifyCaptcha($captcha)) {
        $error = 'CAPTCHA incorrecto';
        recordLoginAttempt(false);
        $showExtraVerification = true;
    }
    // Verificar credenciales
    elseif ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
        $_SESSION['authenticated'] = true;
        $_SESSION['login_time'] = time();
        $_SESSION['admin_level'] = 'extreme';
        recordLoginAttempt(true);

        // Regenerar sesión después del login exitoso
        session_regenerate_id(true);

        header('Location: dashboard_extreme.php');
        exit;
    } else {
        $error = 'Credenciales incorrectas';
        recordLoginAttempt(false);
        $showExtraVerification = true;
    }

    // Regenerar captcha después de cada intento
    $captcha_question = generateAdvancedCaptcha();
}

// Verificar si ya está autenticado
if ($_SESSION['authenticated'] ?? false) {
    // Verificar timeout de sesión (30 minutos)
    if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
        session_destroy();
        header('Location: index_extreme.php');
        exit;
    }

    header('Location: dashboard_extreme.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Sistema de Seguridad Extremo</title>
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet, noimageindex">
    <style>
        * { box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            overflow: hidden;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-width: 450px;
            width: 100%;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .security-header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .security-badge {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 15px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            box-sizing: border-box;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        .error {
            color: #e74c3c;
            margin-bottom: 20px;
            text-align: center;
            background: rgba(231, 76, 60, 0.1);
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #e74c3c;
        }
        .captcha {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 18px;
            box-shadow: 0 5px 15px rgba(240, 147, 251, 0.3);
        }
        .security-info {
            background: rgba(52, 152, 219, 0.1);
            border-left: 4px solid #3498db;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-size: 12px;
            color: #2c3e50;
        }
        .threat-level {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background: rgba(231, 76, 60, 0.1);
            border-radius: 8px;
        }
        .threat-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #e74c3c;
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="security-header">
            <div class="security-badge">🛡️ NIVEL DE SEGURIDAD: EXTREMO</div>
            <h2>Sistema de Acceso Fortificado</h2>
        </div>

        <div class="threat-level">
            <span><strong>Estado de Amenaza:</strong> ALTO</span>
            <div class="threat-indicator"></div>
        </div>

        <?php if ($error): ?>
            <div class="error">🚨 <?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>

        <form method="POST">
            <?php echo createHoneypot(); ?>
            <input type="hidden" name="security_token" value="<?php echo htmlspecialchars($_SESSION['security_token']); ?>">

            <div class="form-group">
                <label for="username">🔐 Usuario Autorizado:</label>
                <input type="text" id="username" name="username" required autocomplete="username">
            </div>

            <div class="form-group">
                <label for="password">🗝️ Clave de Acceso:</label>
                <input type="password" id="password" name="password" required autocomplete="current-password">
            </div>

            <div class="captcha">
                🧮 Verificación Matemática:<br>
                <strong><?php echo $captcha_question; ?> = ?</strong>
            </div>

            <div class="form-group">
                <label for="captcha">Resultado:</label>
                <input type="text" id="captcha" name="captcha" required autocomplete="off">
            </div>

            <button type="submit" name="login">🚀 ACCESO SEGURO</button>
        </form>

        <div class="security-info">
            <strong>🔒 Protecciones Activas:</strong><br>
            ✓ Detección de bots avanzada<br>
            ✓ Protección anti-fuerza bruta<br>
            ✓ Sistema honeypot activo<br>
            ✓ Rate limiting extremo<br>
            ✓ Análisis de amenazas en tiempo real<br>
            ✓ Logging forense completo
        </div>
    </div>

    <script>
        // Protección contra herramientas de desarrollo
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
                e.preventDefault();
                return false;
            }
        });

        // Detectar si las herramientas de desarrollo están abiertas
        let devtools = {open: false, orientation: null};
        setInterval(function() {
            if (window.outerHeight - window.innerHeight > 200 || window.outerWidth - window.innerWidth > 200) {
                if (!devtools.open) {
                    devtools.open = true;
                    console.clear();
                    console.log('%c🛡️ SISTEMA DE SEGURIDAD EXTREMO ACTIVO', 'color: red; font-size: 20px; font-weight: bold;');
                    console.log('%cAcceso no autorizado detectado', 'color: red; font-size: 16px;');
                }
            } else {
                devtools.open = false;
            }
        }, 500);

        // Deshabilitar clic derecho
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });

        // Deshabilitar selección de texto
        document.addEventListener('selectstart', function(e) {
            e.preventDefault();
        });
    </script>
</body>
</html>
