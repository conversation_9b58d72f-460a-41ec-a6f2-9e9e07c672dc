<?php
session_start();

// Verificar autenticación
if (!($_SESSION['authenticated'] ?? false)) {
    header('Location: index.php');
    exit;
}

// Verificar timeout de sesión (30 minutos)
if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Procesar logout
if ($_GET['logout'] ?? false) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// ========================================
// FUNCIONES DE MONITOREO
// ========================================

function getRecentAttempts() {
    $attempts = [];
    $files = glob('login_attempts_*.txt');
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            $data = json_decode(file_get_contents($file), true);
            if ($data['attempts'] > 0) {
                $ip = str_replace(['login_attempts_', '.txt'], '', basename($file));
                $attempts[] = [
                    'ip_hash' => $ip,
                    'attempts' => $data['attempts'],
                    'locked_until' => $data['locked_until']
                ];
            }
        }
    }
    
    return $attempts;
}

function getRateLimitData() {
    $data = [];
    $files = glob('rate_limit_*.txt');
    
    foreach ($files as $file) {
        if (file_exists($file)) {
            $content = json_decode(file_get_contents($file), true);
            if (!empty($content['attempts'])) {
                $ip = str_replace(['rate_limit_', '.txt'], '', basename($file));
                $data[] = [
                    'ip_hash' => $ip,
                    'requests' => count($content['attempts']),
                    'last_request' => max($content['attempts'])
                ];
            }
        }
    }
    
    return $data;
}

function cleanOldFiles() {
    $files = array_merge(glob('login_attempts_*.txt'), glob('rate_limit_*.txt'));
    $cleaned = 0;
    
    foreach ($files as $file) {
        if (filemtime($file) < time() - 86400) { // Más de 24 horas
            unlink($file);
            $cleaned++;
        }
    }
    
    return $cleaned;
}

// Limpiar archivos antiguos si se solicita
if ($_GET['clean'] ?? false) {
    $cleaned = cleanOldFiles();
    $message = "Se limpiaron $cleaned archivos antiguos.";
}

$recentAttempts = getRecentAttempts();
$rateLimitData = getRateLimitData();

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Panel de Control - Sistema Seguro</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: #f5f5f5; 
        }
        .header { 
            background: #007cba; 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            margin-bottom: 20px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
        }
        .card { 
            background: white; 
            padding: 20px; 
            border-radius: 10px; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1); 
            margin-bottom: 20px; 
        }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 20px; 
            margin-bottom: 20px; 
        }
        .stat-card { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 20px; 
            border-radius: 10px; 
            text-align: center; 
        }
        .stat-number { 
            font-size: 2em; 
            font-weight: bold; 
            margin-bottom: 10px; 
        }
        table { 
            width: 100%; 
            border-collapse: collapse; 
            margin-top: 15px; 
        }
        th, td { 
            padding: 12px; 
            text-align: left; 
            border-bottom: 1px solid #ddd; 
        }
        th { 
            background: #f8f9fa; 
            font-weight: bold; 
        }
        .btn { 
            background: #007cba; 
            color: white; 
            padding: 10px 20px; 
            text-decoration: none; 
            border-radius: 5px; 
            display: inline-block; 
            margin: 5px; 
        }
        .btn:hover { 
            background: #005a87; 
        }
        .btn-danger { 
            background: #dc3545; 
        }
        .btn-danger:hover { 
            background: #c82333; 
        }
        .alert { 
            padding: 15px; 
            margin-bottom: 20px; 
            border-radius: 5px; 
            background: #d4edda; 
            color: #155724; 
            border: 1px solid #c3e6cb; 
        }
        .status-locked { 
            color: #dc3545; 
            font-weight: bold; 
        }
        .status-active { 
            color: #28a745; 
            font-weight: bold; 
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Panel de Control de Seguridad</h1>
        <div>
            <span>Sesión activa desde: <?php echo date('H:i:s', $_SESSION['login_time']); ?></span>
            <a href="?logout=1" class="btn btn-danger" style="margin-left: 20px;">Cerrar Sesión</a>
        </div>
    </div>

    <?php if (isset($message)): ?>
        <div class="alert"><?php echo htmlspecialchars($message); ?></div>
    <?php endif; ?>

    <div class="stats">
        <div class="stat-card">
            <div class="stat-number"><?php echo count($recentAttempts); ?></div>
            <div>IPs con Intentos Fallidos</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo count($rateLimitData); ?></div>
            <div>IPs Monitoreadas</div>
        </div>
        <div class="stat-card">
            <div class="stat-number"><?php echo date('H:i:s'); ?></div>
            <div>Hora del Servidor</div>
        </div>
    </div>

    <div class="card">
        <h2>Intentos de Login Fallidos</h2>
        <p>Monitoreo de IPs que han intentado acceder sin éxito:</p>
        
        <?php if (empty($recentAttempts)): ?>
            <p style="color: #28a745; font-weight: bold;">✓ No hay intentos fallidos recientes</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>IP (Hash)</th>
                        <th>Intentos</th>
                        <th>Estado</th>
                        <th>Bloqueado hasta</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentAttempts as $attempt): ?>
                        <tr>
                            <td><?php echo substr($attempt['ip_hash'], 0, 16) . '...'; ?></td>
                            <td><?php echo $attempt['attempts']; ?></td>
                            <td>
                                <?php if ($attempt['locked_until'] > time()): ?>
                                    <span class="status-locked">BLOQUEADA</span>
                                <?php else: ?>
                                    <span class="status-active">ACTIVA</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php 
                                if ($attempt['locked_until'] > time()) {
                                    echo date('H:i:s', $attempt['locked_until']);
                                } else {
                                    echo '-';
                                }
                                ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2>Actividad de Rate Limiting</h2>
        <p>IPs que han realizado múltiples solicitudes:</p>
        
        <?php if (empty($rateLimitData)): ?>
            <p style="color: #28a745; font-weight: bold;">✓ No hay actividad sospechosa</p>
        <?php else: ?>
            <table>
                <thead>
                    <tr>
                        <th>IP (Hash)</th>
                        <th>Solicitudes (última hora)</th>
                        <th>Última solicitud</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($rateLimitData as $data): ?>
                        <tr>
                            <td><?php echo substr($data['ip_hash'], 0, 16) . '...'; ?></td>
                            <td><?php echo $data['requests']; ?></td>
                            <td><?php echo date('H:i:s', $data['last_request']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>

    <div class="card">
        <h2>Acciones de Mantenimiento</h2>
        <p>Herramientas para mantener el sistema limpio y seguro:</p>
        
        <a href="?clean=1" class="btn">Limpiar Archivos Antiguos</a>
        <a href="security_log.php" class="btn">Ver Log de Seguridad</a>
        <a href="?refresh=1" class="btn">Actualizar Datos</a>
    </div>

    <div class="card">
        <h2>Estado del Sistema</h2>
        <ul>
            <li><strong>Protección .htaccess:</strong> ✓ Activa</li>
            <li><strong>Detección de bots:</strong> ✓ Activa</li>
            <li><strong>Rate limiting:</strong> ✓ Activa</li>
            <li><strong>Sistema de login:</strong> ✓ Activa</li>
            <li><strong>Timeout de sesión:</strong> ✓ 30 minutos</li>
            <li><strong>Bloqueo por intentos:</strong> ✓ 3 intentos / 30 min</li>
        </ul>
    </div>

    <script>
        // Auto-refresh cada 30 segundos
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
