<?php
// ========================================
// SISTEMA DE CONTROL DE ACCESO GEOGRÁFICO
// ========================================

session_start();

// Configuración
define('ADMIN_USER', 'admin');
define('ADMIN_PASS', 'admin123');

// Función para obtener información de IP
function getIPInfo($ip) {
    // Usar servicio gratuito para obtener info de IP
    $url = "http://ip-api.com/json/{$ip}?fields=status,message,country,countryCode,region,regionName,city,isp,org,as,query";

    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'user_agent' => 'Mozilla/5.0 (compatible; GeoChecker/1.0)'
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 'success') {
            return $data;
        }
    }

    return [
        'country' => 'Desconocido',
        'countryCode' => 'XX',
        'region' => 'Desconocido',
        'city' => 'Desconocido',
        'isp' => 'Desconocido',
        'org' => 'Desconocido',
        'query' => $ip
    ];
}

// Países bloqueados (Europa)
$paisesEuropaBloqueados = [
    'AD', 'AL', 'AT', 'BA', 'BE', 'BG', 'BY', 'CH', 'CY', 'CZ', 'DE', 'DK',
    'EE', 'ES', 'FI', 'FR', 'GB', 'GE', 'GR', 'HR', 'HU', 'IE', 'IS', 'IT',
    'LI', 'LT', 'LU', 'LV', 'MC', 'MD', 'ME', 'MK', 'MT', 'NL', 'NO', 'PL',
    'PT', 'RO', 'RS', 'RU', 'SE', 'SI', 'SK', 'SM', 'UA', 'VA'
];

// Países permitidos (Latinoamérica y USA)
$paisesPermitidos = [
    'US', 'CA', // Norteamérica
    'MX', 'GT', 'BZ', 'SV', 'HN', 'NI', 'CR', 'PA', // Centroamérica
    'AR', 'BO', 'BR', 'CL', 'CO', 'EC', 'GY', 'PY', 'PE', 'SR', 'UY', 'VE', // Sudamérica
    'CU', 'DO', 'HT', 'JM', 'PR', 'TT' // Caribe
];

// Obtener IP del visitante
$ip = $_SERVER['REMOTE_ADDR'];
if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR'])[0];
} elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
    $ip = $_SERVER['HTTP_X_REAL_IP'];
}

// Obtener información de la IP
$ipInfo = getIPInfo($ip);
$paisCodigo = $ipInfo['countryCode'];

// Registrar la visita
$visitaData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $ip,
    'pais' => $ipInfo['country'],
    'codigo_pais' => $paisCodigo,
    'region' => $ipInfo['regionName'],
    'ciudad' => $ipInfo['city'],
    'isp' => $ipInfo['isp'],
    'organizacion' => $ipInfo['org'],
    'ruta' => $_SERVER['REQUEST_URI'] ?? '/',
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido',
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'Directo',
    'metodo' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
    'acceso' => 'PERMITIDO'
];

// Verificar si el país está bloqueado
if (in_array($paisCodigo, $paisesEuropaBloqueados)) {
    $visitaData['acceso'] = 'BLOQUEADO_EUROPA';

    // Registrar el bloqueo
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    // Mostrar página de bloqueo
    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Acceso Restringido</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
            .error { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
            .info { color: #666; margin-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="error">🚫 Acceso Restringido</h1>
            <p>Lo sentimos, el acceso desde tu ubicación no está permitido.</p>
            <div class="info">
                <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
                <p><strong>IP:</strong> <?php echo htmlspecialchars($ip); ?></p>
                <p><strong>ISP:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?></p>
            </div>
            <p><small>Si crees que esto es un error, contacta al administrador.</small></p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Si llegamos aquí, el acceso está permitido
$visitaData['acceso'] = 'PERMITIDO';
file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);



