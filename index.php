<?php
// Habilitar reporte de errores para diagnóstico
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Iniciar sesión con manejo de errores
try {
    session_start();
} catch (Exception $e) {
    die('Error al iniciar sesión: ' . $e->getMessage());
}

// ========================================
// CONFIGURACIÓN DE SEGURIDAD
// ========================================

// Cambiar estas credenciales por las tuyas
define('ADMIN_USERNAME', 'admin_seguro');
define('ADMIN_PASSWORD', 'TuPassword_Muy_Seguro_2024!');
define('MAX_LOGIN_ATTEMPTS', 3);
define('LOCKOUT_TIME', 1800); // 30 minutos

// ========================================
// DETECCIÓN DE BOTS Y SCRAPERS
// ========================================

function detectBot() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $botPatterns = [
        '/bot/i', '/spider/i', '/crawler/i', '/scraper/i',
        '/wget/i', '/curl/i', '/python/i', '/java/i',
        '/libwww/i', '/httpclient/i', '/scanner/i',
        '/nikto/i', '/sqlmap/i', '/nmap/i'
    ];
    
    foreach ($botPatterns as $pattern) {
        if (preg_match($pattern, $userAgent)) {
            return true;
        }
    }
    
    // Detectar user agents vacíos o sospechosos
    if (empty($userAgent) || strlen($userAgent) < 10) {
        return true;
    }
    
    return false;
}

// ========================================
// SISTEMA DE RATE LIMITING
// ========================================

function checkRateLimit() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'rate_limit_' . md5($ip) . '.txt';
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        $now = time();
        
        // Limpiar intentos antiguos (más de 1 hora)
        $data['attempts'] = array_filter($data['attempts'], function($timestamp) use ($now) {
            return ($now - $timestamp) < 3600;
        });
        
        // Si hay más de 10 intentos en la última hora, bloquear
        if (count($data['attempts']) > 10) {
            return false;
        }
        
        $data['attempts'][] = $now;
    } else {
        $data = ['attempts' => [time()]];
    }
    
    file_put_contents($file, json_encode($data));
    return true;
}

// ========================================
// SISTEMA DE AUTENTICACIÓN
// ========================================

function checkLoginAttempts() {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'login_attempts_' . md5($ip) . '.txt';
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
        
        if ($data['locked_until'] > time()) {
            return false;
        }
        
        if ($data['attempts'] >= MAX_LOGIN_ATTEMPTS) {
            $data['locked_until'] = time() + LOCKOUT_TIME;
            file_put_contents($file, json_encode($data));
            return false;
        }
    }
    
    return true;
}

function recordLoginAttempt($success = false) {
    $ip = $_SERVER['REMOTE_ADDR'];
    $file = 'login_attempts_' . md5($ip) . '.txt';
    
    if (file_exists($file)) {
        $data = json_decode(file_get_contents($file), true);
    } else {
        $data = ['attempts' => 0, 'locked_until' => 0];
    }
    
    if ($success) {
        $data['attempts'] = 0;
        $data['locked_until'] = 0;
    } else {
        $data['attempts']++;
    }
    
    file_put_contents($file, json_encode($data));
}

// ========================================
// VERIFICACIONES DE SEGURIDAD
// ========================================

// Verificar si es un bot
if (detectBot()) {
    http_response_code(403);
    die('Acceso denegado');
}

// Verificar rate limiting
if (!checkRateLimit()) {
    http_response_code(429);
    die('Demasiadas solicitudes. Intenta más tarde.');
}

// Verificar intentos de login
if (!checkLoginAttempts()) {
    http_response_code(423);
    die('IP bloqueada temporalmente por múltiples intentos fallidos.');
}

// ========================================
// PROCESAMIENTO DE LOGIN
// ========================================

$error = '';
$showCaptcha = false;

if ($_POST['login'] ?? false) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    // Verificar CAPTCHA simple
    if ($_SESSION['captcha'] !== $captcha) {
        $error = 'CAPTCHA incorrecto';
        recordLoginAttempt(false);
    } elseif ($username === ADMIN_USERNAME && $password === ADMIN_PASSWORD) {
        $_SESSION['authenticated'] = true;
        $_SESSION['login_time'] = time();
        recordLoginAttempt(true);
        header('Location: dashboard.php');
        exit;
    } else {
        $error = 'Credenciales incorrectas';
        recordLoginAttempt(false);
    }
    
    $showCaptcha = true;
}

// Generar CAPTCHA simple
$num1 = rand(1, 10);
$num2 = rand(1, 10);
$_SESSION['captcha'] = $num1 + $num2;

// ========================================
// VERIFICAR SI YA ESTÁ AUTENTICADO
// ========================================

if ($_SESSION['authenticated'] ?? false) {
    // Verificar timeout de sesión (30 minutos)
    if (time() - ($_SESSION['login_time'] ?? 0) > 1800) {
        session_destroy();
        header('Location: index.php');
        exit;
    }
    
    header('Location: dashboard.php');
    exit;
}

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acceso Seguro</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: #f0f0f0; 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            height: 100vh; 
            margin: 0; 
        }
        .login-container { 
            background: white; 
            padding: 30px; 
            border-radius: 10px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.1); 
            max-width: 400px; 
            width: 100%; 
        }
        .form-group { 
            margin-bottom: 15px; 
        }
        label { 
            display: block; 
            margin-bottom: 5px; 
            font-weight: bold; 
        }
        input[type="text"], input[type="password"] { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 5px; 
            box-sizing: border-box; 
        }
        button { 
            width: 100%; 
            padding: 12px; 
            background: #007cba; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer; 
            font-size: 16px; 
        }
        button:hover { 
            background: #005a87; 
        }
        .error { 
            color: red; 
            margin-bottom: 15px; 
            text-align: center; 
        }
        .captcha { 
            background: #f9f9f9; 
            padding: 10px; 
            border-radius: 5px; 
            text-align: center; 
            margin-bottom: 15px; 
            font-weight: bold; 
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2>Acceso Seguro al Sistema</h2>
        
        <?php if ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
        <?php endif; ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="username">Usuario:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Contraseña:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <div class="captcha">
                Resuelve: <?php echo $num1; ?> + <?php echo $num2; ?> = ?
            </div>
            
            <div class="form-group">
                <label for="captcha">Resultado:</label>
                <input type="text" id="captcha" name="captcha" required>
            </div>
            
            <button type="submit" name="login">Acceder</button>
        </form>
        
        <p style="text-align: center; margin-top: 20px; font-size: 12px; color: #666;">
            Sistema protegido contra accesos no autorizados
        </p>
    </div>
</body>
</html>
