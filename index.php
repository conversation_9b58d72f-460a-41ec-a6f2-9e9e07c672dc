<?php
// ========================================
// SISTEMA DE CONTROL DE ACCESO Y SOLICITUDES
// ========================================

session_start();

// Configuración
define('ADMIN_USER', 'admin');
define('ADMIN_PASS', 'admin123');
define('PAIS_ADMIN', 'PA'); // Solo Panamá puede acceder al panel admin

// Función para obtener información de IP
function getIPInfo($ip) {
    // Usar servicio gratuito para obtener info de IP
    $url = "http://ip-api.com/json/{$ip}?fields=status,message,country,countryCode,region,regionName,city,isp,org,as,query";

    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'user_agent' => 'Mozilla/5.0 (compatible; GeoChecker/1.0)'
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 'success') {
            return $data;
        }
    }

    return [
        'country' => 'Desconocido',
        'countryCode' => 'XX',
        'region' => 'Desconocido',
        'city' => 'Desconocido',
        'isp' => 'Desconocido',
        'org' => 'Desconocido',
        'query' => $ip
    ];
}

// Funciones de control de acceso
function estaEnListaNegra($ip) {
    if (!file_exists('lista_negra.json')) return false;
    $listaNegra = json_decode(file_get_contents('lista_negra.json'), true);
    return isset($listaNegra[$ip]);
}

function tieneAccesoAprobado($ip, $ruta = '/') {
    if (!file_exists('accesos_aprobados.json')) return false;
    $aprobados = json_decode(file_get_contents('accesos_aprobados.json'), true);

    if (!isset($aprobados[$ip])) return false;

    $acceso = $aprobados[$ip];

    // Verificar si el acceso está activo
    if ($acceso['estado'] !== 'activo') return false;

    // Verificar si tiene acceso a esta ruta específica
    if ($acceso['tipo_acceso'] === 'total') return true;

    if ($acceso['tipo_acceso'] === 'limitado') {
        return in_array($ruta, $acceso['rutas_permitidas']);
    }

    return false;
}

function registrarSolicitudAcceso($ip, $ipInfo, $ruta, $motivo = '') {
    $solicitud = [
        'id' => uniqid(),
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $ip,
        'pais' => $ipInfo['country'],
        'ciudad' => $ipInfo['city'],
        'isp' => $ipInfo['isp'],
        'ruta_solicitada' => $ruta,
        'motivo' => $motivo,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'estado' => 'pendiente'
    ];

    $solicitudes = [];
    if (file_exists('solicitudes_acceso.json')) {
        $solicitudes = json_decode(file_get_contents('solicitudes_acceso.json'), true);
    }

    $solicitudes[] = $solicitud;
    file_put_contents('solicitudes_acceso.json', json_encode($solicitudes, JSON_PRETTY_PRINT));

    return $solicitud['id'];
}

// Funciones de seguridad para admin
function verificarRateLimitAdmin($ip) {
    $archivo = 'admin_rate_limit_' . hash('sha256', $ip) . '.json';
    $ahora = time();

    if (file_exists($archivo)) {
        $data = json_decode(file_get_contents($archivo), true);

        // Limpiar intentos antiguos (más de 10 minutos)
        $data['intentos'] = array_filter($data['intentos'], function($timestamp) use ($ahora) {
            return ($ahora - $timestamp) < 600;
        });

        // Si hay más de 5 intentos en 10 minutos, bloquear
        if (count($data['intentos']) >= 5) {
            return false;
        }

        $data['intentos'][] = $ahora;
    } else {
        $data = ['intentos' => [$ahora]];
    }

    file_put_contents($archivo, json_encode($data));
    return true;
}

function registrarIntentoLoginAdmin($ip) {
    $archivo = 'admin_intentos_' . hash('sha256', $ip) . '.json';

    if (file_exists($archivo)) {
        $data = json_decode(file_get_contents($archivo), true);
        $data['total']++;
        $data['ultimo_intento'] = time();
    } else {
        $data = ['total' => 1, 'primer_intento' => time(), 'ultimo_intento' => time()];
    }

    file_put_contents($archivo, json_encode($data));
}

function generarCaptchaAdmin() {
    $num1 = rand(10, 99);
    $num2 = rand(10, 99);
    $operacion = ['+', '-', '*'][rand(0, 2)];

    switch ($operacion) {
        case '+':
            $resultado = $num1 + $num2;
            break;
        case '-':
            $resultado = $num1 - $num2;
            break;
        case '*':
            $resultado = $num1 * $num2;
            break;
    }

    $_SESSION['admin_captcha'] = (string)$resultado;
    $_SESSION['captcha_pregunta'] = "$num1 $operacion $num2";
}

// Países bloqueados (Europa)
$paisesEuropaBloqueados = [
    'AD', 'AL', 'AT', 'BA', 'BE', 'BG', 'BY', 'CH', 'CY', 'CZ', 'DE', 'DK',
    'EE', 'ES', 'FI', 'FR', 'GB', 'GE', 'GR', 'HR', 'HU', 'IE', 'IS', 'IT',
    'LI', 'LT', 'LU', 'LV', 'MC', 'MD', 'ME', 'MK', 'MT', 'NL', 'NO', 'PL',
    'PT', 'RO', 'RS', 'RU', 'SE', 'SI', 'SK', 'SM', 'UA', 'VA'
];

// Países permitidos (Latinoamérica y USA)
$paisesPermitidos = [
    'US', 'CA', // Norteamérica
    'MX', 'GT', 'BZ', 'SV', 'HN', 'NI', 'CR', 'PA', // Centroamérica
    'AR', 'BO', 'BR', 'CL', 'CO', 'EC', 'GY', 'PY', 'PE', 'SR', 'UY', 'VE', // Sudamérica
    'CU', 'DO', 'HT', 'JM', 'PR', 'TT' // Caribe
];

// Obtener IP del visitante
$ip = $_SERVER['REMOTE_ADDR'];
if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR'])[0];
} elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
    $ip = $_SERVER['HTTP_X_REAL_IP'];
}

// Obtener información de la IP
$ipInfo = getIPInfo($ip);
$paisCodigo = $ipInfo['countryCode'];
$rutaActual = $_SERVER['REQUEST_URI'] ?? '/';

// DEBUG: Mostrar información de país (eliminar después de probar)
if (isset($_GET['debug'])) {
    echo "<h3>DEBUG INFO:</h3>";
    echo "<p>País detectado: " . $ipInfo['country'] . "</p>";
    echo "<p>Código de país: " . $paisCodigo . "</p>";
    echo "<p>¿Está en países bloqueados? " . (in_array($paisCodigo, $paisesEuropaBloqueados) ? 'SÍ' : 'NO') . "</p>";
    echo "<p>¿Está en países permitidos? " . (in_array($paisCodigo, $paisesPermitidos) ? 'SÍ' : 'NO') . "</p>";
    exit;
}

// Verificar si está en lista negra
if (estaEnListaNegra($ip)) {
    http_response_code(403);
    die('🚫 IP Bloqueada por el administrador');
}

// Verificar acceso al panel admin (solo desde Panamá)
if (isset($_GET['admin'])) {
    // Verificación 1: Solo desde Panamá
    if ($paisCodigo !== PAIS_ADMIN) {
        // Log del intento de acceso no autorizado
        $intentoHack = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $ip,
            'pais' => $ipInfo['country'],
            'codigo_pais' => $paisCodigo,
            'isp' => $ipInfo['isp'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'tipo' => 'INTENTO_ACCESO_ADMIN_FUERA_PA',
            'referer' => $_SERVER['HTTP_REFERER'] ?? ''
        ];
        file_put_contents('intentos_hack.log', json_encode($intentoHack) . "\n", FILE_APPEND | LOCK_EX);

        http_response_code(403);
        die('🚫 Panel de administración solo accesible desde Panamá');
    }

    // Verificación 2: Detectar bots y herramientas automatizadas
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $botPatterns = [
        '/bot/i', '/spider/i', '/crawler/i', '/scraper/i', '/wget/i', '/curl/i',
        '/python/i', '/java/i', '/libwww/i', '/httpclient/i', '/scanner/i',
        '/postman/i', '/insomnia/i', '/httpie/i', '/requests/i', '/urllib/i'
    ];

    foreach ($botPatterns as $pattern) {
        if (preg_match($pattern, $userAgent)) {
            $intentoBot = [
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $ip,
                'pais' => $ipInfo['country'],
                'user_agent' => $userAgent,
                'tipo' => 'BOT_INTENTO_ADMIN',
                'patron_detectado' => $pattern
            ];
            file_put_contents('intentos_hack.log', json_encode($intentoBot) . "\n", FILE_APPEND | LOCK_EX);

            http_response_code(403);
            die('🚫 Acceso automatizado detectado');
        }
    }

    // Verificación 3: User Agent debe parecer navegador real
    if (empty($userAgent) || strlen($userAgent) < 20) {
        $intentoSospechoso = [
            'timestamp' => date('Y-m-d H:i:s'),
            'ip' => $ip,
            'pais' => $ipInfo['country'],
            'user_agent' => $userAgent,
            'tipo' => 'USER_AGENT_SOSPECHOSO'
        ];
        file_put_contents('intentos_hack.log', json_encode($intentoSospechoso) . "\n", FILE_APPEND | LOCK_EX);

        http_response_code(403);
        die('🚫 Navegador no válido');
    }

    // Logout del admin
    if ($_GET['admin'] === 'logout') {
        session_destroy();
        header('Location: /');
        exit;
    }

    // Procesar login del admin
    if ($_POST['login'] ?? false) {
        $user = $_POST['username'] ?? '';
        $pass = $_POST['password'] ?? '';
        $captcha = $_POST['captcha'] ?? '';
        $expectedCaptcha = $_SESSION['admin_captcha'] ?? '';

        // Verificación 4: CAPTCHA obligatorio
        if ($captcha !== $expectedCaptcha) {
            $intentoLogin = [
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $ip,
                'pais' => $ipInfo['country'],
                'user_agent' => $userAgent,
                'tipo' => 'CAPTCHA_INCORRECTO_ADMIN',
                'captcha_enviado' => $captcha,
                'captcha_esperado' => $expectedCaptcha
            ];
            file_put_contents('intentos_hack.log', json_encode($intentoLogin) . "\n", FILE_APPEND | LOCK_EX);

            $error = 'CAPTCHA incorrecto';
        }
        // Verificación 5: Rate limiting para intentos de login
        elseif (!verificarRateLimitAdmin($ip)) {
            $error = 'Demasiados intentos. Espera 10 minutos.';
        }
        // Verificar credenciales
        elseif ($user === ADMIN_USER && $pass === ADMIN_PASS) {
            // Verificación 6: Generar token de sesión único
            $sessionToken = bin2hex(random_bytes(32));
            $_SESSION['admin'] = true;
            $_SESSION['admin_token'] = $sessionToken;
            $_SESSION['admin_ip'] = $ip;
            $_SESSION['admin_country'] = $paisCodigo;
            $_SESSION['login_time'] = time();

            // Log del login exitoso
            $loginExitoso = [
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $ip,
                'pais' => $ipInfo['country'],
                'tipo' => 'LOGIN_ADMIN_EXITOSO',
                'session_token' => substr($sessionToken, 0, 8) . '...'
            ];
            file_put_contents('intentos_hack.log', json_encode($loginExitoso) . "\n", FILE_APPEND | LOCK_EX);

            header('Location: ?admin=panel');
            exit;
        } else {
            // Log del intento fallido
            $intentoFallido = [
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $ip,
                'pais' => $ipInfo['country'],
                'user_agent' => $userAgent,
                'tipo' => 'LOGIN_ADMIN_FALLIDO',
                'usuario_intentado' => $user
            ];
            file_put_contents('intentos_hack.log', json_encode($intentoFallido) . "\n", FILE_APPEND | LOCK_EX);

            registrarIntentoLoginAdmin($ip);
            $error = 'Credenciales incorrectas';
        }

        // Regenerar CAPTCHA después de cada intento
        generarCaptchaAdmin();
    } else {
        // Generar CAPTCHA inicial
        generarCaptchaAdmin();
    }

    // Mostrar panel admin si está logueado
    if ($_SESSION['admin'] ?? false) {
        // Verificaciones adicionales de seguridad en cada request
        if ($_SESSION['admin_ip'] !== $ip) {
            session_destroy();
            die('🚫 Sesión comprometida - IP cambió');
        }

        if ($_SESSION['admin_country'] !== $paisCodigo) {
            session_destroy();
            die('🚫 Sesión comprometida - País cambió');
        }

        if ((time() - $_SESSION['login_time']) > 3600) { // 1 hora máximo
            session_destroy();
            die('🚫 Sesión expirada');
        }

        include 'admin_panel.php';
        exit;
    }

    // Mostrar formulario de login
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Panel de Administración</title>
        <style>
            body { font-family: Arial, sans-serif; background: #2c3e50; color: white; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
            .login-box { background: #34495e; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.5); }
            input { width: 100%; padding: 10px; margin: 10px 0; border: none; border-radius: 5px; }
            button { width: 100%; padding: 12px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #2980b9; }
            .error { color: #e74c3c; margin-bottom: 15px; }
        </style>
    </head>
    <body>
        <div class="login-box">
            <h2>🔐 Panel de Administración</h2>
            <p>Acceso desde: <?php echo $ipInfo['country']; ?> ✅</p>
            <?php if (isset($error)): ?>
                <div class="error"><?php echo $error; ?></div>
            <?php endif; ?>
            <form method="POST">
                <input type="text" name="username" placeholder="Usuario" required>
                <input type="password" name="password" placeholder="Contraseña" required>

                <div style="background: #2c3e50; padding: 15px; margin: 15px 0; border-radius: 5px; text-align: center;">
                    <strong>Resuelve: <?php echo $_SESSION['captcha_pregunta'] ?? ''; ?> = ?</strong>
                </div>
                <input type="text" name="captcha" placeholder="Resultado del cálculo" required>

                <button type="submit" name="login">Acceder</button>
            </form>

            <div style="margin-top: 20px; font-size: 12px; opacity: 0.7;">
                <p>⚠️ Protecciones activas:</p>
                <ul style="text-align: left; font-size: 11px;">
                    <li>Solo desde Panamá</li>
                    <li>CAPTCHA obligatorio</li>
                    <li>Rate limiting activo</li>
                    <li>Detección de bots</li>
                    <li>Sesión con timeout</li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Registrar la visita
$visitaData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $ip,
    'pais' => $ipInfo['country'],
    'codigo_pais' => $paisCodigo,
    'region' => $ipInfo['regionName'],
    'ciudad' => $ipInfo['city'],
    'isp' => $ipInfo['isp'],
    'organizacion' => $ipInfo['org'],
    'ruta' => $rutaActual,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido',
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'Directo',
    'metodo' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
    'acceso' => 'EVALUANDO'
];

// Verificar si el país está bloqueado (Europa)
if (in_array($paisCodigo, $paisesEuropaBloqueados)) {
    $visitaData['acceso'] = 'BLOQUEADO_EUROPA';
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Acceso Restringido</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
            .error { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="error">🚫 Acceso Restringido</h1>
            <p>Lo sentimos, el acceso desde Europa no está permitido.</p>
            <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Verificar si tiene acceso aprobado
if (tieneAccesoAprobado($ip, $rutaActual)) {
    $visitaData['acceso'] = 'PERMITIDO_APROBADO';
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    // Mostrar contenido permitido
    mostrarContenidoPermitido($ipInfo, $rutaActual);
    exit;
}

// Si no tiene acceso aprobado, mostrar formulario de solicitud
if ($_POST['solicitar_acceso'] ?? false) {
    $motivo = $_POST['motivo'] ?? '';
    $solicitudId = registrarSolicitudAcceso($ip, $ipInfo, $rutaActual, $motivo);

    $visitaData['acceso'] = 'SOLICITUD_ENVIADA';
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Solicitud Enviada</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
            .success { color: #27ae60; font-size: 24px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="success">📧 Solicitud Enviada</h1>
            <p>Tu solicitud de acceso ha sido enviada al administrador.</p>
            <p><strong>ID de Solicitud:</strong> <?php echo $solicitudId; ?></p>
            <p>Recibirás una respuesta pronto.</p>
            <a href="/">Volver al inicio</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Mostrar formulario de solicitud de acceso
$visitaData['acceso'] = 'SOLICITUD_REQUERIDA';
file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

function mostrarContenidoPermitido($ipInfo, $ruta) {
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>RogsMediaTV - Acceso Autorizado</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 50px 0; }
            .content { background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎬 RogsMediaTV</h1>
                <p>Acceso Autorizado</p>
            </div>

            <div class="content">
                <h2>✅ Bienvenido Usuario Autorizado</h2>
                <p>Tu acceso ha sido aprobado por el administrador.</p>
                <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
                <p><strong>ISP:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?></p>

                <h3>📁 Contenido Disponible</h3>
                <ul>
                    <li><a href="/scripts/" style="color: #feca57;">📂 Scripts</a></li>
                    <li><a href="/media/" style="color: #feca57;">🎥 Media</a></li>
                    <li><a href="/docs/" style="color: #feca57;">📄 Documentos</a></li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    <?php
}

// Mostrar formulario de solicitud de acceso
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RogsMediaTV - Solicitar Acceso</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; padding: 50px 0; }
        .form-container { background: rgba(255,255,255,0.1); padding: 40px; border-radius: 15px; margin: 20px 0; }
        .info-box { background: rgba(0,0,0,0.2); padding: 20px; border-radius: 10px; margin: 20px 0; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: bold; }
        input, textarea { width: 100%; padding: 12px; border: none; border-radius: 8px; background: rgba(255,255,255,0.9); color: #333; }
        button { width: 100%; padding: 15px; background: #27ae60; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; }
        button:hover { background: #219a52; }
        .warning { background: rgba(231, 76, 60, 0.2); border-left: 4px solid #e74c3c; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 RogsMediaTV</h1>
            <p>Solicitud de Acceso</p>
        </div>

        <div class="info-box">
            <h3>📍 Tu Información Detectada</h3>
            <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
            <p><strong>Región:</strong> <?php echo htmlspecialchars($ipInfo['regionName']); ?></p>
            <p><strong>Ciudad:</strong> <?php echo htmlspecialchars($ipInfo['city']); ?></p>
            <p><strong>ISP:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?></p>
            <p><strong>IP:</strong> <?php echo htmlspecialchars($ip); ?></p>
            <p><strong>Ruta Solicitada:</strong> <?php echo htmlspecialchars($rutaActual); ?></p>
        </div>

        <div class="warning">
            <h3>⚠️ Acceso Restringido</h3>
            <p>Este contenido requiere autorización previa del administrador. Tu solicitud será revisada manualmente.</p>
        </div>

        <div class="form-container">
            <h2>📝 Solicitar Acceso</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="motivo">Motivo de la Solicitud:</label>
                    <textarea id="motivo" name="motivo" rows="4" placeholder="Explica por qué necesitas acceso a este contenido..." required></textarea>
                </div>

                <button type="submit" name="solicitar_acceso">📧 Enviar Solicitud</button>
            </form>

            <div style="margin-top: 30px; text-align: center; opacity: 0.8;">
                <p><small>Tu solicitud será enviada al administrador para revisión.</small></p>
                <p><small>Tiempo estimado de respuesta: 24-48 horas</small></p>
            </div>
        </div>

        <div class="info-box">
            <h3>ℹ️ Información Importante</h3>
            <ul>
                <li>Solo se permite acceso desde Latinoamérica y Estados Unidos</li>
                <li>Todas las solicitudes son revisadas manualmente</li>
                <li>El acceso puede ser revocado en cualquier momento</li>
                <li>Tu actividad será monitoreada por seguridad</li>
            </ul>
        </div>
    </div>
</body>
</html>

