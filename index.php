<?php
// ========================================
// SISTEMA DE CONTROL DE ACCESO Y SOLICITUDES
// ========================================

session_start();

// Configuración
define('ADMIN_USER', 'admin');
define('ADMIN_PASS', 'admin123');
define('PAIS_ADMIN', 'PA'); // Solo Panamá puede acceder al panel admin

// Función para obtener información de IP
function getIPInfo($ip) {
    // Usar servicio gratuito para obtener info de IP
    $url = "http://ip-api.com/json/{$ip}?fields=status,message,country,countryCode,region,regionName,city,isp,org,as,query";

    $context = stream_context_create([
        'http' => [
            'timeout' => 5,
            'user_agent' => 'Mozilla/5.0 (compatible; GeoChecker/1.0)'
        ]
    ]);

    $response = @file_get_contents($url, false, $context);

    if ($response) {
        $data = json_decode($response, true);
        if ($data && $data['status'] === 'success') {
            return $data;
        }
    }

    return [
        'country' => 'Desconocido',
        'countryCode' => 'XX',
        'region' => 'Desconocido',
        'city' => 'Desconocido',
        'isp' => 'Desconocido',
        'org' => 'Desconocido',
        'query' => $ip
    ];
}

// Funciones de control de acceso
function estaEnListaNegra($ip) {
    if (!file_exists('lista_negra.json')) return false;
    $listaNegra = json_decode(file_get_contents('lista_negra.json'), true);
    return isset($listaNegra[$ip]);
}

function tieneAccesoAprobado($ip, $ruta = '/') {
    if (!file_exists('accesos_aprobados.json')) return false;
    $aprobados = json_decode(file_get_contents('accesos_aprobados.json'), true);

    if (!isset($aprobados[$ip])) return false;

    $acceso = $aprobados[$ip];

    // Verificar si el acceso está activo
    if ($acceso['estado'] !== 'activo') return false;

    // Verificar si tiene acceso a esta ruta específica
    if ($acceso['tipo_acceso'] === 'total') return true;

    if ($acceso['tipo_acceso'] === 'limitado') {
        return in_array($ruta, $acceso['rutas_permitidas']);
    }

    return false;
}

function registrarSolicitudAcceso($ip, $ipInfo, $ruta, $motivo = '') {
    $solicitud = [
        'id' => uniqid(),
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $ip,
        'pais' => $ipInfo['country'],
        'ciudad' => $ipInfo['city'],
        'isp' => $ipInfo['isp'],
        'ruta_solicitada' => $ruta,
        'motivo' => $motivo,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'estado' => 'pendiente'
    ];

    $solicitudes = [];
    if (file_exists('solicitudes_acceso.json')) {
        $solicitudes = json_decode(file_get_contents('solicitudes_acceso.json'), true);
    }

    $solicitudes[] = $solicitud;
    file_put_contents('solicitudes_acceso.json', json_encode($solicitudes, JSON_PRETTY_PRINT));

    return $solicitud['id'];
}

// Países bloqueados (Europa)
$paisesEuropaBloqueados = [
    'AD', 'AL', 'AT', 'BA', 'BE', 'BG', 'BY', 'CH', 'CY', 'CZ', 'DE', 'DK',
    'EE', 'ES', 'FI', 'FR', 'GB', 'GE', 'GR', 'HR', 'HU', 'IE', 'IS', 'IT',
    'LI', 'LT', 'LU', 'LV', 'MC', 'MD', 'ME', 'MK', 'MT', 'NL', 'NO', 'PL',
    'PT', 'RO', 'RS', 'RU', 'SE', 'SI', 'SK', 'SM', 'UA', 'VA'
];

// Países permitidos (Latinoamérica y USA)
$paisesPermitidos = [
    'US', 'CA', // Norteamérica
    'MX', 'GT', 'BZ', 'SV', 'HN', 'NI', 'CR', 'PA', // Centroamérica
    'AR', 'BO', 'BR', 'CL', 'CO', 'EC', 'GY', 'PY', 'PE', 'SR', 'UY', 'VE', // Sudamérica
    'CU', 'DO', 'HT', 'JM', 'PR', 'TT' // Caribe
];

// Obtener IP del visitante
$ip = $_SERVER['REMOTE_ADDR'];
if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR'])[0];
} elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
    $ip = $_SERVER['HTTP_X_REAL_IP'];
}

// Obtener información de la IP
$ipInfo = getIPInfo($ip);
$paisCodigo = $ipInfo['countryCode'];
$rutaActual = $_SERVER['REQUEST_URI'] ?? '/';

// Verificar si está en lista negra
if (estaEnListaNegra($ip)) {
    http_response_code(403);
    die('🚫 IP Bloqueada por el administrador');
}

// Verificar acceso al panel admin (solo desde Panamá)
if (isset($_GET['admin'])) {
    if ($paisCodigo !== PAIS_ADMIN) {
        http_response_code(403);
        die('🚫 Panel de administración solo accesible desde Panamá');
    }

    // Logout del admin
    if ($_GET['admin'] === 'logout') {
        session_destroy();
        header('Location: /');
        exit;
    }

    // Procesar login del admin
    if ($_POST['login'] ?? false) {
        $user = $_POST['username'] ?? '';
        $pass = $_POST['password'] ?? '';

        if ($user === ADMIN_USER && $pass === ADMIN_PASS) {
            $_SESSION['admin'] = true;
            header('Location: ?admin=panel');
            exit;
        } else {
            $error = 'Credenciales incorrectas';
        }
    }

    // Mostrar panel admin si está logueado
    if ($_SESSION['admin'] ?? false) {
        include 'admin_panel.php';
        exit;
    }

    // Mostrar formulario de login
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Panel de Administración</title>
        <style>
            body { font-family: Arial, sans-serif; background: #2c3e50; color: white; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
            .login-box { background: #34495e; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.5); }
            input { width: 100%; padding: 10px; margin: 10px 0; border: none; border-radius: 5px; }
            button { width: 100%; padding: 12px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer; }
            button:hover { background: #2980b9; }
            .error { color: #e74c3c; margin-bottom: 15px; }
        </style>
    </head>
    <body>
        <div class="login-box">
            <h2>🔐 Panel de Administración</h2>
            <p>Acceso desde: <?php echo $ipInfo['country']; ?> ✅</p>
            <?php if (isset($error)): ?>
                <div class="error"><?php echo $error; ?></div>
            <?php endif; ?>
            <form method="POST">
                <input type="text" name="username" placeholder="Usuario" required>
                <input type="password" name="password" placeholder="Contraseña" required>
                <button type="submit" name="login">Acceder</button>
            </form>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Registrar la visita
$visitaData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $ip,
    'pais' => $ipInfo['country'],
    'codigo_pais' => $paisCodigo,
    'region' => $ipInfo['regionName'],
    'ciudad' => $ipInfo['city'],
    'isp' => $ipInfo['isp'],
    'organizacion' => $ipInfo['org'],
    'ruta' => $rutaActual,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Desconocido',
    'referer' => $_SERVER['HTTP_REFERER'] ?? 'Directo',
    'metodo' => $_SERVER['REQUEST_METHOD'] ?? 'GET',
    'acceso' => 'EVALUANDO'
];

// Verificar si el país está bloqueado (Europa)
if (in_array($paisCodigo, $paisesEuropaBloqueados)) {
    $visitaData['acceso'] = 'BLOQUEADO_EUROPA';
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Acceso Restringido</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
            .error { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="error">🚫 Acceso Restringido</h1>
            <p>Lo sentimos, el acceso desde Europa no está permitido.</p>
            <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Verificar si tiene acceso aprobado
if (tieneAccesoAprobado($ip, $rutaActual)) {
    $visitaData['acceso'] = 'PERMITIDO_APROBADO';
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    // Mostrar contenido permitido
    mostrarContenidoPermitido($ipInfo, $rutaActual);
    exit;
}

// Si no tiene acceso aprobado, mostrar formulario de solicitud
if ($_POST['solicitar_acceso'] ?? false) {
    $motivo = $_POST['motivo'] ?? '';
    $solicitudId = registrarSolicitudAcceso($ip, $ipInfo, $rutaActual, $motivo);

    $visitaData['acceso'] = 'SOLICITUD_ENVIADA';
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Solicitud Enviada</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
            .success { color: #27ae60; font-size: 24px; margin-bottom: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="success">📧 Solicitud Enviada</h1>
            <p>Tu solicitud de acceso ha sido enviada al administrador.</p>
            <p><strong>ID de Solicitud:</strong> <?php echo $solicitudId; ?></p>
            <p>Recibirás una respuesta pronto.</p>
            <a href="/">Volver al inicio</a>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Mostrar formulario de solicitud de acceso
$visitaData['acceso'] = 'SOLICITUD_REQUERIDA';
file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

function mostrarContenidoPermitido($ipInfo, $ruta) {
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>RogsMediaTV - Acceso Autorizado</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; padding: 50px 0; }
            .content { background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎬 RogsMediaTV</h1>
                <p>Acceso Autorizado</p>
            </div>

            <div class="content">
                <h2>✅ Bienvenido Usuario Autorizado</h2>
                <p>Tu acceso ha sido aprobado por el administrador.</p>
                <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
                <p><strong>ISP:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?></p>

                <h3>📁 Contenido Disponible</h3>
                <ul>
                    <li><a href="/scripts/" style="color: #feca57;">📂 Scripts</a></li>
                    <li><a href="/media/" style="color: #feca57;">🎥 Media</a></li>
                    <li><a href="/docs/" style="color: #feca57;">📄 Documentos</a></li>
                </ul>
            </div>
        </div>
    </body>
    </html>
    <?php
}

// Mostrar formulario de solicitud de acceso
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RogsMediaTV - Solicitar Acceso</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; padding: 50px 0; }
        .form-container { background: rgba(255,255,255,0.1); padding: 40px; border-radius: 15px; margin: 20px 0; }
        .info-box { background: rgba(0,0,0,0.2); padding: 20px; border-radius: 10px; margin: 20px 0; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: bold; }
        input, textarea { width: 100%; padding: 12px; border: none; border-radius: 8px; background: rgba(255,255,255,0.9); color: #333; }
        button { width: 100%; padding: 15px; background: #27ae60; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px; font-weight: bold; }
        button:hover { background: #219a52; }
        .warning { background: rgba(231, 76, 60, 0.2); border-left: 4px solid #e74c3c; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 RogsMediaTV</h1>
            <p>Solicitud de Acceso</p>
        </div>

        <div class="info-box">
            <h3>📍 Tu Información Detectada</h3>
            <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
            <p><strong>Región:</strong> <?php echo htmlspecialchars($ipInfo['regionName']); ?></p>
            <p><strong>Ciudad:</strong> <?php echo htmlspecialchars($ipInfo['city']); ?></p>
            <p><strong>ISP:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?></p>
            <p><strong>IP:</strong> <?php echo htmlspecialchars($ip); ?></p>
            <p><strong>Ruta Solicitada:</strong> <?php echo htmlspecialchars($rutaActual); ?></p>
        </div>

        <div class="warning">
            <h3>⚠️ Acceso Restringido</h3>
            <p>Este contenido requiere autorización previa del administrador. Tu solicitud será revisada manualmente.</p>
        </div>

        <div class="form-container">
            <h2>📝 Solicitar Acceso</h2>
            <form method="POST">
                <div class="form-group">
                    <label for="motivo">Motivo de la Solicitud:</label>
                    <textarea id="motivo" name="motivo" rows="4" placeholder="Explica por qué necesitas acceso a este contenido..." required></textarea>
                </div>

                <button type="submit" name="solicitar_acceso">📧 Enviar Solicitud</button>
            </form>

            <div style="margin-top: 30px; text-align: center; opacity: 0.8;">
                <p><small>Tu solicitud será enviada al administrador para revisión.</small></p>
                <p><small>Tiempo estimado de respuesta: 24-48 horas</small></p>
            </div>
        </div>

        <div class="info-box">
            <h3>ℹ️ Información Importante</h3>
            <ul>
                <li>Solo se permite acceso desde Latinoamérica y Estados Unidos</li>
                <li>Todas las solicitudes son revisadas manualmente</li>
                <li>El acceso puede ser revocado en cualquier momento</li>
                <li>Tu actividad será monitoreada por seguridad</li>
            </ul>
        </div>
    </div>
</body>
</html>

// Verificar si el país está bloqueado
if (in_array($paisCodigo, $paisesEuropaBloqueados)) {
    $visitaData['acceso'] = 'BLOQUEADO_EUROPA';

    // Registrar el bloqueo
    file_put_contents('accesos.log', json_encode($visitaData) . "\n", FILE_APPEND | LOCK_EX);

    // Mostrar página de bloqueo
    http_response_code(403);
    ?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <title>Acceso Restringido</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; background: #f0f0f0; }
            .container { max-width: 600px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; }
            .error { color: #e74c3c; font-size: 24px; margin-bottom: 20px; }
            .info { color: #666; margin-bottom: 10px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1 class="error">🚫 Acceso Restringido</h1>
            <p>Lo sentimos, el acceso desde tu ubicación no está permitido.</p>
            <div class="info">
                <p><strong>País:</strong> <?php echo htmlspecialchars($ipInfo['country']); ?></p>
                <p><strong>IP:</strong> <?php echo htmlspecialchars($ip); ?></p>
                <p><strong>ISP:</strong> <?php echo htmlspecialchars($ipInfo['isp']); ?></p>
            </div>
            <p><small>Si crees que esto es un error, contacta al administrador.</small></p>
        </div>
    </body>
    </html>