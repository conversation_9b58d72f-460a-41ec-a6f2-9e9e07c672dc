<?php
// ========================================
// HONEYPOT AVANZADO - TRAMPA PARA ATACANTES
// ========================================

// Este archivo actúa como una trampa. Cualquier acceso aquí es malicioso.

$ip = $_SERVER['REMOTE_ADDR'];
$userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$referer = $_SERVER['HTTP_REFERER'] ?? '';

// Log detallado del atacante
$attackData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'ip' => $ip,
    'ip_hash' => hash('sha256', $ip),
    'type' => 'HONEYPOT_TRIGGERED',
    'severity' => 'CRITICAL',
    'details' => 'Acceso a honeypot detectado - Actividad maliciosa confirmada',
    'user_agent' => $userAgent,
    'request_uri' => $requestUri,
    'method' => $method,
    'referer' => $referer,
    'headers' => getallheaders(),
    'post_data' => $_POST,
    'get_data' => $_GET,
    'cookies' => $_COOKIE
];

// Guardar en log de seguridad
file_put_contents('security_extreme.log', json_encode($attackData) . "\n", FILE_APPEND | LOCK_EX);

// Guardar en log específico de honeypot
file_put_contents('honeypot_attacks.log', json_encode($attackData) . "\n", FILE_APPEND | LOCK_EX);

// Bloquear IP inmediatamente por 24 horas
$banFile = 'honeypot_ban_' . hash('sha256', $ip) . '.txt';
file_put_contents($banFile, time() + 86400); // 24 horas

// Incrementar contador de ataques para esta IP
$attackCountFile = 'attack_count_' . hash('sha256', $ip) . '.json';
if (file_exists($attackCountFile)) {
    $data = json_decode(file_get_contents($attackCountFile), true);
    $data['count']++;
    $data['last_attack'] = time();
} else {
    $data = ['count' => 1, 'first_attack' => time(), 'last_attack' => time()];
}
file_put_contents($attackCountFile, json_encode($data));

// Si es un ataque repetido, ban permanente
if ($data['count'] >= 3) {
    $permanentBanFile = 'permanent_ban_' . hash('sha256', $ip) . '.txt';
    file_put_contents($permanentBanFile, time());
    
    // Log del ban permanente
    $banLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $ip,
        'ip_hash' => hash('sha256', $ip),
        'type' => 'PERMANENT_BAN',
        'severity' => 'CRITICAL',
        'details' => "IP baneada permanentemente después de {$data['count']} ataques al honeypot",
        'attack_count' => $data['count']
    ];
    file_put_contents('security_extreme.log', json_encode($banLog) . "\n", FILE_APPEND | LOCK_EX);
}

// Simular diferentes tipos de respuestas para confundir al atacante
$responses = [
    'fake_admin_panel',
    'fake_login_form',
    'fake_database_error',
    'fake_file_listing',
    'fake_api_response'
];

$responseType = $responses[array_rand($responses)];

// Headers para hacer creer que es un sitio real
header('Server: Apache/2.4.41 (Ubuntu)');
header('X-Powered-By: PHP/7.4.3');

switch ($responseType) {
    case 'fake_admin_panel':
        // Simular un panel de administración falso
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Admin Panel - Login</title>
            <style>
                body { font-family: Arial, sans-serif; background: #f0f0f0; }
                .login-box { width: 300px; margin: 100px auto; background: white; padding: 30px; border-radius: 5px; }
                input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; }
                button { width: 100%; padding: 10px; background: #007cba; color: white; border: none; }
            </style>
        </head>
        <body>
            <div class="login-box">
                <h2>Administrator Login</h2>
                <form method="post">
                    <input type="text" name="username" placeholder="Username" required>
                    <input type="password" name="password" placeholder="Password" required>
                    <button type="submit">Login</button>
                </form>
                <p style="color: red;">Invalid credentials. Please try again.</p>
            </div>
        </body>
        </html>
        <?php
        break;
        
    case 'fake_login_form':
        // Formulario de login falso que siempre falla
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Required</title>
        </head>
        <body>
            <h1>Access Restricted</h1>
            <p>Please enter your credentials:</p>
            <form method="post">
                <p>Username: <input type="text" name="user"></p>
                <p>Password: <input type="password" name="pass"></p>
                <p><input type="submit" value="Login"></p>
            </form>
            <?php if ($_POST): ?>
                <p style="color: red;">Authentication failed. Access denied.</p>
            <?php endif; ?>
        </body>
        </html>
        <?php
        break;
        
    case 'fake_database_error':
        // Error de base de datos falso
        http_response_code(500);
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Database Error</title>
        </head>
        <body>
            <h1>Database Connection Error</h1>
            <p><strong>MySQL Error:</strong> Access denied for user 'admin'@'localhost' (using password: YES)</p>
            <p><strong>Error Code:</strong> 1045</p>
            <p><strong>File:</strong> /var/www/html/config/database.php</p>
            <p><strong>Line:</strong> 23</p>
            <hr>
            <p><em>Please contact the system administrator.</em></p>
        </body>
        </html>
        <?php
        break;
        
    case 'fake_file_listing':
        // Listado de archivos falso
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>Index of /backup</title>
        </head>
        <body>
            <h1>Index of /backup</h1>
            <hr>
            <pre>
            <a href="../">../</a>
            <a href="database_backup.sql">database_backup.sql</a>          15-Jan-2024 10:30    2.5M
            <a href="config_backup.tar.gz">config_backup.tar.gz</a>        14-Jan-2024 15:45    1.2M
            <a href="users.csv">users.csv</a>                    13-Jan-2024 09:15    856K
            <a href="passwords.txt">passwords.txt</a>                12-Jan-2024 14:20    45K
            <a href="admin_keys.pem">admin_keys.pem</a>               11-Jan-2024 11:30    4.1K
            </pre>
            <hr>
            <address>Apache/2.4.41 (Ubuntu) Server at <?php echo $_SERVER['HTTP_HOST']; ?> Port 443</address>
        </body>
        </html>
        <?php
        break;
        
    case 'fake_api_response':
        // Respuesta de API falsa
        header('Content-Type: application/json');
        $fakeData = [
            'status' => 'error',
            'message' => 'Unauthorized access',
            'error_code' => 401,
            'timestamp' => time(),
            'users' => [
                ['id' => 1, 'username' => 'admin', 'role' => 'administrator'],
                ['id' => 2, 'username' => 'user1', 'role' => 'user'],
                ['id' => 3, 'username' => 'guest', 'role' => 'guest']
            ],
            'debug_info' => [
                'server' => 'prod-server-01',
                'database' => 'mysql://localhost:3306/main_db',
                'version' => '2.1.4'
            ]
        ];
        echo json_encode($fakeData, JSON_PRETTY_PRINT);
        break;
}

// Delay adicional para hacer perder tiempo al atacante
sleep(rand(2, 5));

// Si el atacante intenta hacer POST, capturar y loggear los datos
if ($_POST) {
    $postLog = [
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $ip,
        'ip_hash' => hash('sha256', $ip),
        'type' => 'HONEYPOT_POST_DATA',
        'severity' => 'CRITICAL',
        'details' => 'Datos POST capturados en honeypot',
        'post_data' => $_POST,
        'user_agent' => $userAgent
    ];
    file_put_contents('honeypot_posts.log', json_encode($postLog) . "\n", FILE_APPEND | LOCK_EX);
}

exit;
?>
