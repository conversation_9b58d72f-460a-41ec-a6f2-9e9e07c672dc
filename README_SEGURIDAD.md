# 🛡️ Sistema de Seguridad Avanzado para Hostinger

## ⚠️ IMPORTANTE - CONFIGURACIÓN INICIAL

### 1. Cambiar Credenciales de Acceso
**ANTES DE SUBIR A PRODUCCIÓN**, edita el archivo `index.php` y cambia estas líneas:

```php
define('ADMIN_USERNAME', 'admin_seguro');
define('ADMIN_PASSWORD', 'TuPassword_Muy_Seguro_2024!');
```

**Usa credenciales únicas y seguras.**

### 2. Permisos de Archivos
Configura los permisos correctos en tu hosting:
- `.htaccess`: 644
- `*.php`: 644
- Directorio principal: 755

### 3. Configuración de Hostinger
En el panel de Hostinger:
1. Ve a "Archivos" → "Administrador de archivos"
2. Sube todos los archivos al directorio `public_html`
3. Verifica que el `.htaccess` esté activo

## 🔒 Características de Seguridad

### Protección .htaccess
- ✅ Bloqueo de listado de directorios
- ✅ Protección de archivos sensibles (.htaccess, .ini, .log, etc.)
- ✅ Detección y bloqueo de bots maliciosos
- ✅ Protección contra inyección SQL
- ✅ Protección contra XSS
- ✅ Headers de seguridad avanzados
- ✅ Rate limiting básico
- ✅ Bloqueo de métodos HTTP peligrosos

### Sistema de Autenticación
- ✅ Login con usuario y contraseña
- ✅ CAPTCHA matemático simple
- ✅ Bloqueo por intentos fallidos (3 intentos = 30 min bloqueado)
- ✅ Timeout de sesión (30 minutos)
- ✅ Rate limiting por IP

### Detección de Amenazas
- ✅ Detección automática de bots y scrapers
- ✅ Análisis de User Agents sospechosos
- ✅ Monitoreo de intentos de acceso
- ✅ Log de eventos de seguridad
- ✅ Dashboard de monitoreo en tiempo real

## 📊 Uso del Sistema

### Acceso Inicial
1. Ve a tu dominio: `https://tudominio.com`
2. Ingresa las credenciales configuradas
3. Resuelve el CAPTCHA matemático
4. Accede al dashboard de seguridad

### Dashboard de Seguridad
- **Estadísticas en tiempo real** de intentos de acceso
- **Monitoreo de IPs** con actividad sospechosa
- **Log de eventos** detallado
- **Herramientas de mantenimiento**

### Log de Seguridad
- Registro detallado de todos los eventos
- Análisis de tipos de amenazas
- Información de IPs atacantes
- User Agents y detalles técnicos

## 🚨 Qué Bloquea el Sistema

### Bots y Scrapers
- Wget, cURL, Python scripts
- Scrapy, BeautifulSoup
- Nikto, SQLMap, Nmap
- User Agents vacíos o sospechosos

### Ataques Comunes
- Inyección SQL
- Cross-Site Scripting (XSS)
- Directory Traversal
- Acceso a archivos de configuración
- Métodos HTTP peligrosos (TRACE, DELETE)

### Rate Limiting
- Máximo 10 solicitudes por hora por IP
- Bloqueo automático de IPs abusivas
- Timeout de conexión limitado

## 🔧 Configuraciones Adicionales

### Bloqueo por País (Opcional)
Descomenta en `.htaccess`:
```apache
RewriteCond %{ENV:GEOIP_COUNTRY_CODE} ^(CN|RU|KP)$
RewriteRule .* - [F,L]
```

### IPs Específicas a Bloquear
Añade en `.htaccess`:
```apache
RewriteCond %{REMOTE_ADDR} ^123\.456\.789\.000$
RewriteRule .* - [F,L]
```

### Personalizar Timeouts
En `index.php` y `dashboard.php`, modifica:
```php
define('LOCKOUT_TIME', 1800); // 30 minutos
define('MAX_LOGIN_ATTEMPTS', 3); // 3 intentos
```

## 📁 Estructura de Archivos

```
/
├── .htaccess                 # Protecciones del servidor
├── index.php               # Página de login seguro
├── dashboard.php            # Panel de control
├── security_log.php         # Visor de logs
├── README_SEGURIDAD.md      # Este archivo
├── login_attempts_*.txt     # Archivos de intentos (auto-generados)
├── rate_limit_*.txt         # Archivos de rate limiting (auto-generados)
└── security_events.log      # Log de eventos (auto-generado)
```

## 🛠️ Mantenimiento

### Limpieza Automática
- Los archivos de log se limpian automáticamente después de 24 horas
- Usa el botón "Limpiar Archivos Antiguos" en el dashboard

### Monitoreo Regular
- Revisa el dashboard diariamente
- Analiza los logs de seguridad semanalmente
- Actualiza las credenciales mensualmente

### Backup de Configuración
- Haz backup del `.htaccess` antes de modificarlo
- Guarda las credenciales en un lugar seguro
- Documenta cualquier cambio personalizado

## 🆘 Solución de Problemas

### Error 500 - Internal Server Error
1. Verifica que el `.htaccess` tenga permisos 644
2. Revisa si tu hosting soporta todas las directivas
3. Comenta líneas del `.htaccess` una por una para identificar el problema

### No Puedo Acceder
1. Verifica las credenciales en `index.php`
2. Revisa si tu IP está bloqueada
3. Borra los archivos `login_attempts_*.txt`

### El Sistema No Bloquea Bots
1. Verifica que el `.htaccess` esté activo
2. Revisa los logs del servidor
3. Asegúrate de que mod_rewrite esté habilitado

## 📞 Soporte

Si necesitas ayuda adicional:
1. Revisa los logs de error de tu hosting
2. Contacta al soporte de Hostinger si hay problemas de configuración
3. Documenta cualquier error específico que encuentres

---

**⚠️ RECORDATORIO IMPORTANTE:**
- Cambia las credenciales por defecto ANTES de usar en producción
- Mantén backups regulares de tu configuración
- Monitorea regularmente los logs de seguridad
- Actualiza las protecciones según nuevas amenazas
