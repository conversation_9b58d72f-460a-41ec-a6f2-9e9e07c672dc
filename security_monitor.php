<?php
// Monitor de seguridad - Solo accesible desde el panel admin

session_start();

// Verificar que sea admin autenticado
if (!($_SESSION['admin'] ?? false)) {
    die('Acceso denegado');
}

// Verificar IP y país
$ip = $_SERVER['REMOTE_ADDR'];
if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
    $ip = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR'])[0];
}

if (($_SESSION['admin_ip'] ?? '') !== $ip) {
    session_destroy();
    die('Sesión comprometida');
}

// Función para leer logs de intentos de hack
function getLogs($archivo, $limite = 50) {
    if (!file_exists($archivo)) return [];
    
    $lineas = file($archivo, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $logs = [];
    
    foreach (array_slice($lineas, -$limite) as $linea) {
        $log = json_decode($linea, true);
        if ($log) $logs[] = $log;
    }
    
    return array_reverse($logs);
}

// Función para obtener estadísticas
function getEstadisticas() {
    $stats = [
        'intentos_admin_hoy' => 0,
        'bots_detectados_hoy' => 0,
        'ips_sospechosas' => 0,
        'total_intentos' => 0
    ];
    
    if (file_exists('intentos_hack.log')) {
        $logs = getLogs('intentos_hack.log', 1000);
        $hoy = date('Y-m-d');
        
        foreach ($logs as $log) {
            $stats['total_intentos']++;
            
            if (strpos($log['timestamp'], $hoy) === 0) {
                if (strpos($log['tipo'], 'ADMIN') !== false) {
                    $stats['intentos_admin_hoy']++;
                }
                if (strpos($log['tipo'], 'BOT') !== false) {
                    $stats['bots_detectados_hoy']++;
                }
            }
        }
    }
    
    return $stats;
}

$logs = getLogs('intentos_hack.log', 100);
$stats = getEstadisticas();

?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Monitor de Seguridad</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #1a1a1a; color: white; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { background: #dc3545; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #343a40; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #dc3545; }
        .logs-section { background: #343a40; padding: 20px; border-radius: 10px; }
        table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        th, td { padding: 10px; text-align: left; border-bottom: 1px solid #555; font-size: 12px; }
        th { background: #495057; }
        .tipo-critico { color: #dc3545; font-weight: bold; }
        .tipo-warning { color: #ffc107; }
        .tipo-info { color: #17a2b8; }
        .btn { padding: 8px 15px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .alert-danger { background: #721c24; border-left: 4px solid #dc3545; padding: 15px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Monitor de Seguridad</h1>
            <p>Detección de intentos de hack y actividad sospechosa</p>
            <a href="?admin=panel" class="btn">← Volver al Panel</a>
        </div>

        <?php if ($stats['intentos_admin_hoy'] > 0): ?>
            <div class="alert-danger">
                <strong>⚠️ ALERTA:</strong> Se han detectado <?php echo $stats['intentos_admin_hoy']; ?> intentos de acceso al panel admin hoy.
            </div>
        <?php endif; ?>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['intentos_admin_hoy']; ?></div>
                <div>Intentos Admin Hoy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['bots_detectados_hoy']; ?></div>
                <div>Bots Detectados Hoy</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $stats['total_intentos']; ?></div>
                <div>Total Intentos</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count($logs); ?></div>
                <div>Eventos Recientes</div>
            </div>
        </div>

        <div class="logs-section">
            <h2>📋 Log de Intentos de Hack (Últimos 100)</h2>
            
            <?php if (empty($logs)): ?>
                <p style="color: #28a745;">✅ No se han detectado intentos de hack</p>
            <?php else: ?>
                <table>
                    <thead>
                        <tr>
                            <th>Fecha/Hora</th>
                            <th>IP</th>
                            <th>País</th>
                            <th>Tipo de Intento</th>
                            <th>User Agent</th>
                            <th>Detalles</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($logs as $log): ?>
                            <tr>
                                <td><?php echo $log['timestamp']; ?></td>
                                <td><?php echo $log['ip']; ?></td>
                                <td><?php echo $log['pais'] ?? 'N/A'; ?></td>
                                <td>
                                    <?php 
                                    $clase = 'tipo-info';
                                    if (strpos($log['tipo'], 'ADMIN') !== false) $clase = 'tipo-critico';
                                    elseif (strpos($log['tipo'], 'BOT') !== false) $clase = 'tipo-warning';
                                    ?>
                                    <span class="<?php echo $clase; ?>"><?php echo $log['tipo']; ?></span>
                                </td>
                                <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                    <?php echo htmlspecialchars(substr($log['user_agent'] ?? 'N/A', 0, 50)); ?>
                                </td>
                                <td>
                                    <?php if (isset($log['patron_detectado'])): ?>
                                        Patrón: <?php echo $log['patron_detectado']; ?>
                                    <?php elseif (isset($log['usuario_intentado'])): ?>
                                        Usuario: <?php echo $log['usuario_intentado']; ?>
                                    <?php elseif (isset($log['captcha_enviado'])): ?>
                                        CAPTCHA: <?php echo $log['captcha_enviado']; ?> vs <?php echo $log['captcha_esperado']; ?>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <div class="logs-section" style="margin-top: 30px;">
            <h2>🔍 Análisis de Patrones</h2>
            
            <?php
            // Análisis de IPs más activas
            $ips = [];
            foreach ($logs as $log) {
                $ip = $log['ip'];
                if (!isset($ips[$ip])) {
                    $ips[$ip] = ['count' => 0, 'tipos' => [], 'pais' => $log['pais'] ?? 'N/A'];
                }
                $ips[$ip]['count']++;
                $ips[$ip]['tipos'][] = $log['tipo'];
            }
            
            arsort($ips);
            $topIPs = array_slice($ips, 0, 10, true);
            ?>
            
            <h3>🎯 Top 10 IPs Sospechosas</h3>
            <table>
                <thead>
                    <tr>
                        <th>IP</th>
                        <th>País</th>
                        <th>Intentos</th>
                        <th>Tipos de Ataque</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($topIPs as $ip => $data): ?>
                        <tr>
                            <td><?php echo $ip; ?></td>
                            <td><?php echo $data['pais']; ?></td>
                            <td><span class="tipo-critico"><?php echo $data['count']; ?></span></td>
                            <td><?php echo implode(', ', array_unique($data['tipos'])); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Auto-refresh cada 30 segundos
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>
